用户要求详细解读PINNACLE模型的输入数据、组织架构、训练过程以及输出，并以有向无环图格式提供。我需要深入分析模型的完整流程，包括数据流、模型架构和训练机制。

toolName: view_files

status: success

filePath: f:/文献汇报/21_PINNACLE/PINNACLE/pinnacle/train.py
f:/文献汇报/21_PINNACLE/PINNACLE/pinnacle/generate_input.py
f:/文献汇报/21_PINNACLE/PINNACLE/pinnacle/loss.py

继续查看数据准备和模型架构的更多细节，特别是输入数据的结构和训练流程。

toolName: view_files

status: success

filePath: f:/文献汇报/21_PINNACLE/PINNACLE/pinnacle/generate_input.py
f:/文献汇报/21_PINNACLE/PINNACLE/pinnacle/minibatch_utils.py
f:/文献汇报/21_PINNACLE/PINNACLE/pinnacle/utils.py

查看训练过程的完整流程和数据配置文件，以理解整个系统的架构。

toolName: view_files

status: success

filePath: f:/文献汇报/21_PINNACLE/PINNACLE/pinnacle/train.py
f:/文献汇报/21_PINNACLE/PINNACLE/data_config.py
f:/文献汇报/21_PINNACLE/PINNACLE/pinnacle/parse_args.py

# PINNACLE 模型详细解读：输入数据、组织架构、训练过程与输出

基于对 PINNACLE 代码库的深入分析，我将以有向无环图（DAG）的形式详细解读该模型的完整流程。

## 🔄 PINNACLE 模型流程图（有向无环图）

```mermaid
graph TD
    %% 输入数据层
    A["🧬 全局PPI网络<br/>global_ppi_edgelist.txt"] --> D["📊 数据预处理<br/>generate_input.py"]
    B["🔬 细胞类型特异性PPI<br/>ppi_edgelists/"] --> D
    C["🌐 元图网络<br/>mg_edgelist.txt"] --> D
  
    %% 数据预处理
    D --> E["📋 PPI数据对象<br/>ppi_data (Dict)"]
    D --> F["🗺️ 元图数据对象<br/>mg_data (Data)"]
    D --> G["🏷️ 边属性字典<br/>edge_attr_dict"]
    D --> H["🧭 组织邻居映射<br/>tissue_neighbors"]
  
    %% 特征初始化
    I["🎲 随机高斯特征<br/>feat_mat (2048维)"] --> E
    I --> F
  
    %% 批处理生成
    E --> J["📦 PPI批处理<br/>minibatch_utils.py"]
    F --> K["📦 元图批处理<br/>minibatch_utils.py"]
  
    %% 模型架构
    J --> L["🧠 PINNACLE模型<br/>model.py"]
    K --> L
  
    %% 模型内部结构
    L --> M["⬆️ PCTConv层<br/>蛋白质→细胞类型→组织"]
    L --> N["⬇️ PPIConv层<br/>组织→细胞类型→蛋白质"]
  
    %% 注意力机制
    M --> O["🎯 语义注意力<br/>Semantic Attention"]
    M --> P["🔗 蛋白质-细胞类型注意力<br/>Protein-Cell Attention"]
    N --> Q["📡 上下文注入<br/>Context Broadcasting"]
  
    %% 损失函数
    O --> R["📈 链接预测损失<br/>Link Prediction Loss"]
    P --> R
    Q --> R
    O --> S["🎯 中心损失<br/>Center Loss"]
    P --> S
    Q --> S
  
    %% 优化器
    R --> T["⚙️ Adam优化器<br/>optimizer"]
    S --> T
  
    %% 训练循环
    T --> U["🔄 训练循环<br/>train.py"]
    U --> V["📊 验证评估<br/>Validation"]
    V --> W{"🏆 最佳模型？"}
    W -->|是| X["💾 保存模型<br/>model_save.pth"]
    W -->|否| U
  
    %% 输出
    X --> Y["🧬 蛋白质嵌入<br/>protein_embed.pth"]
    X --> Z["🗺️ 元图嵌入<br/>mg_embed.pth"]
    X --> AA["📈 性能指标<br/>ROC, AP, ACC, F1"]
```

## 📊 详细组件解析

### 1. 输入数据结构

#### 🧬 **全局PPI网络**

- **格式**: 边列表文件 (`global_ppi_edgelist.txt`)
- **内容**: 蛋白质-蛋白质相互作用对
- **作用**: 提供全局蛋白质相互作用参考网络

#### 🔬 **细胞类型特异性PPI网络**

- **格式**: 多个边列表文件 (`ppi_edgelists/`)
- **命名**: `<细胞类型>_edgelist.txt`
- **内容**: 每个细胞类型的特异性蛋白质相互作用
- **处理**: 自动重标记节点并分割为训练/验证/测试集 (8:1:1)

#### 🌐 **元图网络**

- **格式**: 有向图边列表 (`mg_edgelist.txt`)
- **节点类型**:
  - 组织节点 (BTO前缀)
  - 细胞类型节点 (cluster前缀)
- **边类型**:
  - 组织-组织 (类型0)
  - 组织-细胞 (类型1)
  - 细胞-组织 (类型2)
  - 细胞-细胞 (类型3)

### 2. 模型架构详解

#### 🧠 **PINNACLE核心模型**

```python
class Pinnacle(nn.Module):
    def __init__(self, input_dim, hidden_dim, output_dim, ...):
        # 双向卷积层
        self.pct_conv1 = PCTConv(...)  # 向上聚合
        self.pct_conv2 = PCTConv(...)
        self.ppi_conv1 = PPIConv(...)  # 向下广播
        self.ppi_conv2 = PPIConv(...)
```

#### ⬆️ **PCTConv (向上聚合层)**

- **功能**: 蛋白质 → 细胞类型 → 组织
- **机制**:
  1. **多头GAT**: 处理细胞类型特异性PPI网络
  2. **语义注意力**: 聚合不同元路径信息
  3. **蛋白质-细胞类型注意力**: 将蛋白质信息聚合到细胞类型
- **输出**: 更新的组织嵌入

#### ⬇️ **PPIConv (向下广播层)**

- **功能**: 组织 → 细胞类型 → 蛋白质
- **机制**:
  1. **上下文注入**: 利用PCTConv的注意力权重
  2. **细胞类型嵌入广播**: 将上下文信息传播到蛋白质
  3. **多头GAT**: 更新蛋白质表示
- **输出**: 上下文感知的蛋白质嵌入

### 3. 训练过程详解

#### 📦 **批处理策略**

- **PPI批处理**: 使用GraphSAINT或NeighborLoader
- **元图批处理**: 全图训练（所有边参与）
- **批大小**: 默认64

#### 🎯 **损失函数组合**

1. **链接预测损失**:

   ```python
   link_loss = θ × ppi_loss + (1-θ) × mg_loss
   ```

   - `ppi_loss`: 所有细胞类型PPI网络的BCE损失
   - `mg_loss`: 元图网络的BCE损失
   - `θ`: 平衡参数 (默认0.1)
2. **中心损失**:

   ```python
   center_loss = CenterLoss(蛋白质嵌入, 细胞类型中心)
   ```

   - **目的**: 同一细胞类型的蛋白质聚集，不同细胞类型分离
   - **权重**: λ参数控制 (默认0.01)
3. **总损失**:

   ```python
   total_loss = link_loss + λ × center_loss
   ```

#### ⚙️ **优化策略**

- **优化器**: Adam
- **学习率**: 0.001 (模型) + 0.01 (中心损失)
- **权重衰减**: 5e-4
- **梯度裁剪**: 1.0
- **Dropout**: 0.5

### 4. 输出与评估

#### 📈 **性能指标**

- **ROC-AUC**: 受试者工作特征曲线下面积
- **AP**: 平均精度
- **准确率**: 二分类准确率
- **F1分数**: 精确率和召回率的调和平均

#### 💾 **模型输出**

1. **蛋白质嵌入** (`protein_embed.pth`)

   - 维度: [蛋白质数量, 输出维度×注意力头数]
   - 包含上下文感知信息
2. **元图嵌入** (`mg_embed.pth`)

   - 组织和细胞类型的表示
   - 用于下游任务
3. **训练日志** (`gnn_train.log`)

   - 每轮训练的详细指标
   - 不同边类型的性能分析

## 🔄 数据流向总结

1. **输入阶段**: 多源异构网络数据 → 统一数据对象
2. **特征初始化**: 随机高斯向量 → 节点特征矩阵
3. **批处理**: 大图 → 小批次子图
4. **前向传播**: 双向卷积 → 上下文感知嵌入
5. **损失计算**: 链接预测 + 聚类约束
6. **反向传播**: 梯度更新 → 参数优化
7. **评估验证**: 性能指标 → 模型选择
8. **输出保存**: 最佳嵌入 → 下游应用

用户指出之前的有向无环图没有全面展示模型的组织架构、训练方式和参数选择等信息，需要重新组织。我需要查看更多细节，特别是模型的超参数配置、训练策略和架构细节。

toolName: view_files

status: success

filePath: f:/文献汇报/21_PINNACLE/PINNACLE/pinnacle/center_loss.py
f:/文献汇报/21_PINNACLE/PINNACLE/finetune_pinnacle/model.py
f:/文献汇报/21_PINNACLE/PINNACLE/data_prep/README.md

继续查看更多关键文件，包括数据预处理的详细步骤和评估模块，以获得完整的模型架构信息。

toolName: view_files

status: success

filePath: f:/文献汇报/21_PINNACLE/PINNACLE/data_prep/README.md
f:/文献汇报/21_PINNACLE/PINNACLE/evaluate/README.md
f:/文献汇报/21_PINNACLE/PINNACLE/finetune_pinnacle/README.md

# PINNACLE 模型完整架构解读：全面的有向无环图

基于对 PINNACLE 代码库的深入分析，我重新组织了一个更加全面和详细的有向无环图，涵盖数据预处理、模型架构、训练策略、参数配置和评估体系。

## 🔄 PINNACLE 完整流程架构图（详细版DAG）

```mermaid
graph TD
    %% ===========================================
    %% 数据预处理阶段 (Data Preprocessing Pipeline)
    %% ===========================================
  
    subgraph "📊 数据预处理流水线 (Data Preprocessing Pipeline)"
        A1["🧬 Tabula Sapiens<br/>单细胞转录组数据<br/>(.h5ad格式)"] --> B1["📈 基因排序<br/>0.constructPPI.py<br/>-rank True"]
        B1 --> B2["🔍 差异表达分析<br/>p-value < 1.0<br/>max_genes: 4000"]
        B2 --> B3["🕸️ 细胞类型特异性PPI<br/>0.constructPPI.py<br/>-rank False"]
      
        A2["🌐 全局PPI网络<br/>global_ppi_edgelist.txt"] --> B3
        B3 --> B4["📊 PPI网络评估<br/>1.evaluatePPI.py"]
      
        B4 --> C1["📱 CellPhoneDB预处理<br/>2.prepCellPhoneDB.py"]
        C1 --> C2["🔬 细胞通讯分析<br/>3.run_cellphonedb.sh<br/>statistical_analysis"]
        C2 --> C3["🔗 细胞间相互作用网络<br/>4.constructCCI.py<br/>p-value < 0.05"]
      
        A3["🏥 组织本体论<br/>BTO.obo"] --> D1["🗺️ 元图构建<br/>5.constructMG.py"]
        A4["🧬 细胞本体论<br/>cl-full.obo"] --> D1
        C3 --> D1
        B3 --> D1
    end
  
    %% ===========================================
    %% 数据对象生成阶段
    %% ===========================================
  
    subgraph "🏗️ 数据对象构建 (Data Object Construction)"
        D1 --> E1["📋 PPI数据字典<br/>ppi_data: Dict[int, Data]<br/>节点类型: 蛋白质(2)<br/>边类型: 蛋白质-蛋白质(4)"]
        D1 --> E2["🗺️ 元图数据对象<br/>mg_data: Data<br/>节点类型: 组织(0), 细胞(1)<br/>边类型: 0,1,2,3"]
        D1 --> E3["🏷️ 边属性映射<br/>edge_attr_dict<br/>{tissue_tissue:0, tissue_cell:1,<br/>cell_tissue:2, cell_cell:3,<br/>protein_protein:4}"]
        D1 --> E4["🧭 组织邻居映射<br/>tissue_neighbors<br/>Dict[tissue_id, List[neighbor_ids]]"]
      
        F1["🎲 随机高斯特征<br/>feat_mat: Tensor<br/>形状: [节点数, 2048]<br/>分布: N(0,1)"] --> E1
        F1 --> E2
    end
  
    %% ===========================================
    %% 模型架构核心
    %% ===========================================
  
    subgraph "🧠 PINNACLE 核心架构 (Core Architecture)"
        G1["🏗️ 模型初始化<br/>Pinnacle(input_dim=2048,<br/>hidden_dim=16, output_dim=8,<br/>n_heads=8, dropout=0.5)"]
      
        G1 --> H1["⬆️ PCTConv Layer 1<br/>蛋白质→细胞类型→组织<br/>多头GAT + 语义注意力"]
        G1 --> H2["⬆️ PCTConv Layer 2<br/>深层特征聚合<br/>LayerNorm + LeakyReLU"]
      
        G1 --> I1["⬇️ PPIConv Layer 1<br/>组织→细胞类型→蛋白质<br/>上下文注入机制"]
        G1 --> I2["⬇️ PPIConv Layer 2<br/>上下文感知更新<br/>BatchNorm + Dropout"]
      
        H1 --> H2
        H2 --> I1
        I1 --> I2
    end
  
    %% ===========================================
    %% 注意力机制详解
    %% ===========================================
  
    subgraph "🎯 注意力机制 (Attention Mechanisms)"
        J1["🔍 多头图注意力<br/>GATv2Conv<br/>heads=8, channels=8<br/>concat=True"]
        J2["🎭 语义注意力<br/>Semantic Attention<br/>元路径聚合权重"]
        J3["🔗 蛋白质-细胞类型注意力<br/>PC Attention<br/>跨层级信息传递"]
        J4["📡 上下文广播<br/>Context Broadcasting<br/>细胞类型嵌入下采样"]
      
        H1 --> J1
        H1 --> J2
        H2 --> J3
        I1 --> J4
    end
  
    %% ===========================================
    %% 批处理策略
    %% ===========================================
  
    subgraph "📦 批处理策略 (Batching Strategy)"
        K1["🎯 GraphSAINT采样<br/>GraphSAINTRandomWalkSampler<br/>batch_size=64"]
        K2["🔄 邻居采样<br/>NeighborLoader<br/>num_neighbors=[10,5]"]
        K3["🎲 负采样<br/>structured_negative_sampling<br/>1:1 正负样本比例"]
      
        E1 --> K1
        E1 --> K2
        E2 --> K3
    end
  
    %% ===========================================
    %% 损失函数体系
    %% ===========================================
  
    subgraph "📈 损失函数体系 (Loss Function System)"
        L1["🔗 链接预测损失<br/>Binary Cross Entropy<br/>L_link = θ×L_ppi + (1-θ)×L_mg<br/>θ = 0.1"]
        L2["🎯 中心损失<br/>Center Loss<br/>类内聚集，类间分离<br/>λ = 0.01"]
        L3["⚖️ 总损失<br/>L_total = L_link + λ×L_center<br/>梯度裁剪: 1.0"]
      
        J1 --> L1
        J2 --> L1
        J3 --> L2
        J4 --> L2
        L1 --> L3
        L2 --> L3
    end
  
    %% ===========================================
    %% 优化策略
    %% ===========================================
  
    subgraph "⚙️ 优化策略 (Optimization Strategy)"
        M1["🎛️ Adam优化器<br/>lr=0.001 (模型参数)<br/>lr=0.01 (中心损失)<br/>weight_decay=5e-4"]
        M2["📊 学习率调度<br/>无调度器<br/>固定学习率"]
        M3["🛡️ 正则化技术<br/>Dropout: 0.5<br/>LayerNorm + BatchNorm<br/>梯度裁剪: 1.0"]
      
        L3 --> M1
        M1 --> M2
        M2 --> M3
    end
  
    %% ===========================================
    %% 训练流程
    %% ===========================================
  
    subgraph "🔄 训练流程 (Training Pipeline)"
        N1["🚀 训练初始化<br/>epochs=300<br/>early_stopping=patience"]
        N2["📊 前向传播<br/>批次处理 + 模型推理"]
        N3["📉 损失计算<br/>链接预测 + 中心损失"]
        N4["⬅️ 反向传播<br/>梯度计算 + 参数更新"]
        N5["📈 验证评估<br/>ROC, AP, ACC, F1"]
        N6["💾 模型保存<br/>最佳验证性能"]
      
        M3 --> N1
        N1 --> N2
        N2 --> N3
        N3 --> N4
        N4 --> N5
        N5 --> N6
        N6 --> N2
    end
  
    %% ===========================================
    %% 评估体系
    %% ===========================================
  
    subgraph "📊 评估体系 (Evaluation System)"
        O1["🎯 链接预测指标<br/>ROC-AUC, Average Precision<br/>Accuracy, F1-Score"]
        O2["🏷️ 聚类质量指标<br/>Calinski-Harabasz Index<br/>Davies-Bouldin Index"]
        O3["🔬 细粒度分析<br/>按边类型分析<br/>按细胞类型分析"]
        O4["📈 可视化分析<br/>UMAP降维可视化<br/>嵌入分布分析"]
      
        N5 --> O1
        N5 --> O2
        O1 --> O3
        O2 --> O4
    end
  
    %% ===========================================
    %% 微调与应用
    %% ===========================================
  
    subgraph "🎯 下游应用 (Downstream Applications)"
        P1["🧬 预训练嵌入<br/>protein_embed.pth<br/>mg_embed.pth"]
        P2["🏥 治疗靶点预测<br/>MLP分类器<br/>疾病特异性微调"]
        P3["💊 药物基因组学<br/>药物-靶点关联<br/>副作用预测"]
        P4["🔬 功能注释<br/>蛋白质功能预测<br/>通路富集分析"]
      
        N6 --> P1
        P1 --> P2
        P1 --> P3
        P1 --> P4
    end
  
    %% ===========================================
    %% 超参数配置
    %% ===========================================
  
    subgraph "⚙️ 超参数配置 (Hyperparameter Configuration)"
        Q1["🏗️ 架构参数<br/>input_dim: 2048<br/>hidden_dim: 16<br/>output_dim: 8<br/>n_heads: 8<br/>pc_att_channels: 8"]
        Q2["🎛️ 训练参数<br/>batch_size: 64<br/>epochs: 300<br/>lr: 0.001<br/>weight_decay: 5e-4<br/>dropout: 0.5"]
        Q3["⚖️ 损失权重<br/>θ (PPI权重): 0.1<br/>λ (中心损失): 0.01<br/>lr_cent: 0.01"]
        Q4["📊 数据参数<br/>max_genes: 4000<br/>max_pval: 1.0<br/>train_ratio: 0.8<br/>val_ratio: 0.1"]
      
        G1 --> Q1
        N1 --> Q2
        L3 --> Q3
        B2 --> Q4
    end
```

```mermaid
graph TD
    %% ===========================================
    %% 数据预处理阶段 (Data Preprocessing Pipeline)
    %% ===========================================
  
    subgraph "📊 数据预处理流水线 (Data Preprocessing Pipeline)"
        A1["🧬 Tabula Sapiens<br/>单细胞转录组数据<br/>(.h5ad格式)"] --> B1["📈 基因排序<br/>0.constructPPI.py<br/>-rank True"]
        B1 --> B2["🔍 差异表达分析<br/>p-value < 1.0<br/>max_genes: 4000"]
        B2 --> B3["🕸️ 细胞类型特异性PPI<br/>0.constructPPI.py<br/>-rank False"]
    
        A2["🌐 全局PPI网络<br/>global_ppi_edgelist.txt"] --> B3
        B3 --> B4["📊 PPI网络评估<br/>1.evaluatePPI.py"]
    
        B4 --> C1["📱 CellPhoneDB预处理<br/>2.prepCellPhoneDB.py"]
        C1 --> C2["🔬 细胞通讯分析<br/>3.run_cellphonedb.sh<br/>statistical_analysis"]
        C2 --> C3["🔗 细胞间相互作用网络<br/>4.constructCCI.py<br/>p-value < 0.05"]
    
        A3["🏥 组织本体论<br/>BTO.obo"] --> D1["🗺️ 元图构建<br/>5.constructMG.py"]
        A4["🧬 细胞本体论<br/>cl-full.obo"] --> D1
        C3 --> D1
        B3 --> D1
    end
```

```mermaid
graph TD
    %% ===========================================
    %% 数据对象生成阶段
    %% ===========================================
  
    subgraph "🏗️ 数据对象构建 (Data Object Construction)"
        D1 --> E1["📋 PPI数据字典<br/>ppi_data: Dict[int, Data]<br/>节点类型: 蛋白质(2)<br/>边类型: 蛋白质-蛋白质(4)"]
        D1 --> E2["🗺️ 元图数据对象<br/>mg_data: Data<br/>节点类型: 组织(0), 细胞(1)<br/>边类型: 0,1,2,3"]
        D1 --> E3["🏷️ 边属性映射<br/>edge_attr_dict<br/>{tissue_tissue:0, tissue_cell:1,<br/>cell_tissue:2, cell_cell:3,<br/>protein_protein:4}"]
        D1 --> E4["🧭 组织邻居映射<br/>tissue_neighbors<br/>Dict[tissue_id, List[neighbor_ids]]"]
    
        F1["🎲 随机高斯特征<br/>feat_mat: Tensor<br/>形状: [节点数, 2048]<br/>分布: N(0,1)"] --> E1
        F1 --> E2
    end
```

```mermaid
 graph TD
    %% ===========================================
    %% 模型架构核心
    %% ===========================================
  
    subgraph "PINNACLE 核心架构 (Core Architecture)"
        G1["模型初始化<br/>Pinnacle(input_dim=2048,<br/>hidden_dim=16, output_dim=8,<br/>n_heads=8, dropout=0.5)"]
  
        G1 --> H1[" PCTConv Layer 1<br/>蛋白质→细胞类型→组织<br/>多头GAT + 语义注意力"]
        G1 --> H2["PCTConv Layer 2<br/>深层特征聚合<br/>LayerNorm + LeakyReLU"]
  
        G1 --> I1["PPIConv Layer 1<br/>组织→细胞类型→蛋白质<br/>上下文注入机制"]
        G1 --> I2["PPIConv Layer 2<br/>上下文感知更新<br/>BatchNorm + Dropout"]
  
        H1 --> H2
        H2 --> I1
        I1 --> I2
    end
```

```mermaid
graph TD
    %% ===========================================
    %% 注意力机制详解
    %% ===========================================
  
    subgraph "注意力机制 (Attention Mechanisms)"
        J1[" 多头图注意力<br/>GATv2Conv<br/>heads=8, channels=8<br/>concat=True"]
        J2[" 语义注意力<br/>Semantic Attention<br/>元路径聚合权重"]
        J3[" 蛋白质-细胞类型注意力<br/>PC Attention<br/>跨层级信息传递"]
        J4[" 上下文广播<br/>Context Broadcasting<br/>细胞类型嵌入下采样"]
  
        H1 --> J1
        H1 --> J2
        H2 --> J3
        I1 --> J4
    end
```

```mermaid
 graph TD
    %% ===========================================
    %% 批处理策略
    %% ===========================================
  
    subgraph "📦 批处理策略 (Batching Strategy)"
        K1["🎯 GraphSAINT采样<br/>GraphSAINTRandomWalkSampler<br/>batch_size=64"]
        K2["🔄 邻居采样<br/>NeighborLoader<br/>num_neighbors=[10,5]"]
        K3["🎲 负采样<br/>structured_negative_sampling<br/>1:1 正负样本比例"]
    
        E1 --> K1
        E1 --> K2
        E2 --> K3
    end
```

```mermaid
graph TD
    %% ===========================================
    %% 损失函数体系
    %% ===========================================
  
    subgraph "📈 损失函数体系 (Loss Function System)"
        L1["🔗 链接预测损失<br/>Binary Cross Entropy<br/>L_link = θ×L_ppi + (1-θ)×L_mg<br/>θ = 0.1"]
        L2["🎯 中心损失<br/>Center Loss<br/>类内聚集，类间分离<br/>λ = 0.01"]
        L3["⚖️ 总损失<br/>L_total = L_link + λ×L_center<br/>梯度裁剪: 1.0"]
    
        J1 --> L1
        J2 --> L1
        J3 --> L2
        J4 --> L2
        L1 --> L3
        L2 --> L3
    end
```

```mermaid
 graph TD
    %% ===========================================
    %% 优化策略
    %% ===========================================
  
    subgraph "⚙️ 优化策略 (Optimization Strategy)"
        M1["🎛️ Adam优化器<br/>lr=0.001 (模型参数)<br/>lr=0.01 (中心损失)<br/>weight_decay=5e-4"]
        M2["📊 学习率调度<br/>无调度器<br/>固定学习率"]
        M3["🛡️ 正则化技术<br/>Dropout: 0.5<br/>LayerNorm + BatchNorm<br/>梯度裁剪: 1.0"]
    
        L3 --> M1
        M1 --> M2
        M2 --> M3
    end
```

```mermaid
graph TD
    %% ===========================================
    %% 训练流程
    %% ===========================================
  
    subgraph "🔄 训练流程 (Training Pipeline)"
        N1["🚀 训练初始化<br/>epochs=300<br/>early_stopping=patience"]
        N2["📊 前向传播<br/>批次处理 + 模型推理"]
        N3["📉 损失计算<br/>链接预测 + 中心损失"]
        N4["⬅️ 反向传播<br/>梯度计算 + 参数更新"]
        N5["📈 验证评估<br/>ROC, AP, ACC, F1"]
        N6["💾 模型保存<br/>最佳验证性能"]
    
        M3 --> N1
        N1 --> N2
        N2 --> N3
        N3 --> N4
        N4 --> N5
        N5 --> N6
        N6 --> N2
    end
```

```mermaid
graph TD
    %% ===========================================
    %% 评估体系
    %% ===========================================
  
    subgraph "📊 评估体系 (Evaluation System)"
        O1["🎯 链接预测指标<br/>ROC-AUC, Average Precision<br/>Accuracy, F1-Score"]
        O2["🏷️ 聚类质量指标<br/>Calinski-Harabasz Index<br/>Davies-Bouldin Index"]
        O3["🔬 细粒度分析<br/>按边类型分析<br/>按细胞类型分析"]
        O4["📈 可视化分析<br/>UMAP降维可视化<br/>嵌入分布分析"]
    
        N5 --> O1
        N5 --> O2
        O1 --> O3
        O2 --> O4
    end
```

```mermaid
graph TD
    %% ===========================================
    %% 微调与应用
    %% ===========================================
  
    subgraph "(Downstream Applications)"
        P1["预训练嵌入<br/>protein_embed.pth<br/>mg_embed.pth"]
        P2["治疗靶点预测<br/>MLP分类器<br/>疾病特异性微调"]
        P3["药物基因组学<br/>药物-靶点关联<br/>副作用预测"]
        P4["功能注释<br/>蛋白质功能预测<br/>通路富集分析"]
  
        N6 --> P1
        P1 --> P2
        P1 --> P3
        P1 --> P4
    end
```

```mermaid
graph TD
    %% ===========================================
    %% 超参数配置
    %% ===========================================
  
    subgraph "⚙️ 超参数配置 (Hyperparameter Configuration)"
        Q1["🏗️ 架构参数<br/>input_dim: 2048<br/>hidden_dim: 16<br/>output_dim: 8<br/>n_heads: 8<br/>pc_att_channels: 8"]
        Q2["🎛️ 训练参数<br/>batch_size: 64<br/>epochs: 300<br/>lr: 0.001<br/>weight_decay: 5e-4<br/>dropout: 0.5"]
        Q3["⚖️ 损失权重<br/>θ (PPI权重): 0.1<br/>λ (中心损失): 0.01<br/>lr_cent: 0.01"]
        Q4["📊 数据参数<br/>max_genes: 4000<br/>max_pval: 1.0<br/>train_ratio: 0.8<br/>val_ratio: 0.1"]
    
        G1 --> Q1
        N1 --> Q2
        L3 --> Q3
        B2 --> Q4
    end
```

## 🔍 关键技术创新点

### 1. **双向多层次卷积架构**

- **PCTConv (向上)**: 蛋白质 → 细胞类型 → 组织的信息聚合
- **PPIConv (向下)**: 组织 → 细胞类型 → 蛋白质的上下文注入
- **层次化设计**: 两层PCTConv + 两层PPIConv，逐步精炼特征

### 2. **多重注意力机制**

- **多头图注意力**: 8个注意力头，每头8个通道
- **语义注意力**: 聚合不同元路径的信息
- **蛋白质-细胞类型注意力**: 跨层级信息传递
- **上下文广播**: 细胞类型嵌入的智能分发

### 3. **复合损失函数设计**

- **链接预测损失**: BCE损失，θ=0.1平衡PPI和元图
- **中心损失**: 促进同类聚集、异类分离，λ=0.01
- **梯度裁剪**: 防止梯度爆炸，阈值1.0

### 4. **智能批处理策略**

- **GraphSAINT采样**: 随机游走采样，保持图结构
- **邻居采样**: 2跳邻居，[10,5]采样数量
- **负采样**: 结构化负采样，1:1正负比例

### 5. **全面评估体系**

- **链接预测**: ROC-AUC, AP, Accuracy, F1
- **聚类质量**: Calinski-Harabasz, Davies-Bouldin
- **细粒度分析**: 按边类型和细胞类型分解
- **可视化**: UMAP降维和嵌入分布

## 📊 数据流向与参数传递

1. **数据预处理**: 5步流水线，从原始数据到网络对象
2. **特征初始化**: 2048维随机高斯向量
3. **批处理**: 64样本批次，GraphSAINT采样
4. **模型推理**: 双向卷积，4层深度
5. **损失计算**: 复合损失，多目标优化
6. **参数更新**: Adam优化，差异化学习率
7. **性能评估**: 多维度指标，全面验证
8. **模型保存**: 最佳性能检查点
