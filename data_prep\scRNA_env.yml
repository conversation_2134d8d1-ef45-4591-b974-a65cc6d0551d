name: scRNA_env
channels:
  - bioconda
  - anaconda
  - conda-forge
  - defaults
dependencies:
  - _libgcc_mutex=0.1
  - alabaster=0.7.12
  - anndata=0.7.5
  - asciitree=0.3.3
  - astor=0.8.1
  - autograd=1.3
  - autograd-gamma=0.5.0
  - babel=2.9.0
  - beautifulsoup4=4.9.3
  - blas=1.0
  - blosc=1.20.1
  - brotlipy=0.7.0
  - bzip2=1.0.8
  - c-ares=1.17.1
  - ca-certificates=2022.07.19
  - cached-property=1.5.1
  - cairo=1.16.0
  - certifi=2022.6.15
  - cffi=1.14.4
  - chardet=3.0.4
  - colorama=0.4.4
  - cryptography=3.2.1
  - cycler=0.10.0
  - decorator=4.4.2
  - docutils=0.16
  - fasteners=0.14.1
  - fontconfig=2.13.1
  - formulaic=0.2.4
  - freetype=2.10.4
  - future=0.18.2
  - get_version=2.1
  - glib=2.66.1
  - gmp=6.2.1
  - h5py=3.1.0
  - hdf5=1.10.6
  - icu=67.1
  - igraph=0.8.3
  - imagesize=1.2.0
  - importlib-metadata=3.1.0
  - importlib_metadata=3.1.0
  - interface_meta=1.2.3
  - jinja2=2.11.2
  - joblib=0.17.0
  - jpeg=9d
  - kiwisolver=1.3.1
  - krb5=1.17.2
  - lcms2=2.11
  - ld_impl_linux-64=2.33.1
  - legacy-api-wrap=1.2
  - leidenalg=0.8.3
  - libblas=3.9.0
  - libcblas=3.9.0
  - libcurl=7.71.1
  - libedit=3.1.20191231
  - libev=4.33
  - libffi=3.3
  - libgcc-ng=9.1.0
  - libgfortran-ng=7.5.0
  - libgfortran4=7.5.0
  - libiconv=1.16
  - liblapack=3.9.0
  - libllvm10=10.0.1
  - libnghttp2=1.41.0
  - libopenblas=0.3.12
  - libpng=1.6.37
  - libssh2=1.9.0
  - libstdcxx-ng=9.1.0
  - libtiff=4.1.0
  - libuuid=2.32.1
  - libwebp-base=1.1.0
  - libxcb=1.13
  - libxml2=2.9.10
  - lifelines=0.26.0
  - llvmlite=0.34.0
  - louvain=0.6.1
  - lz4-c=1.9.2
  - lzo=2.10
  - markupsafe=1.1.1
  - matplotlib-base=3.3.3
  - mock=4.0.2
  - monotonic=1.5
  - msgpack-python=1.0.1
  - natsort=7.1.0
  - ncurses=6.2
  - networkx=2.5
  - numba=0.51.2
  - numcodecs=0.7.2
  - numexpr=2.7.1
  - numpy=1.19.4
  - olefile=0.46
  - openssl=1.1.1q
  - packaging=20.4
  - pandas=1.1.4
  - patsy=0.5.1
  - pcre=8.44
  - pillow=8.0.1
  - pip=20.2.4
  - pixman=0.38.0
  - pthread-stubs=0.4
  - pycairo=1.20.0
  - pycparser=2.20
  - pygments=2.7.2
  - pyopenssl=20.0.0
  - pyparsing=2.4.7
  - pysocks=1.7.1
  - pytables=3.6.1
  - python=3.8.2
  - python-dateutil=2.8.1
  - python-igraph=0.8.3
  - python_abi=3.8
  - pytz=2020.4
  - readline=8.0
  - scanpy=1.6.0
  - scikit-learn=0.23.2
  - scipy=1.7.3
  - seaborn=0.11.0
  - seaborn-base=0.11.0
  - setuptools=50.3.1
  - setuptools-scm=4.1.2
  - setuptools_scm=4.1.2
  - sinfo=0.3.1
  - six=1.15.0
  - snowballstemmer=2.0.0
  - soupsieve=2.0.1
  - sphinx=3.3.1
  - sphinxcontrib-applehelp=1.0.2
  - sphinxcontrib-devhelp=1.0.2
  - sphinxcontrib-htmlhelp=1.0.3
  - sphinxcontrib-jsmath=1.0.1
  - sphinxcontrib-qthelp=1.0.3
  - sphinxcontrib-serializinghtml=1.1.4
  - sqlite=3.33.0
  - statsmodels=0.12.1
  - stdlib-list=0.7.0
  - tbb=2020.2
  - texttable=1.6.3
  - threadpoolctl=2.1.0
  - tk=8.6.10
  - toml=0.10.2
  - tornado=6.1
  - umap-learn=0.4.6
  - wheel=0.35.1
  - wrapt=1.12.1
  - xlrd=1.2.0
  - xorg-kbproto=1.0.7
  - xorg-libice=1.0.10
  - xorg-libsm=1.2.3
  - xorg-libx11=1.6.12
  - xorg-libxau=1.0.9
  - xorg-libxdmcp=1.1.3
  - xorg-libxext=1.3.4
  - xorg-libxrender=0.9.10
  - xorg-renderproto=0.11.1
  - xorg-xextproto=7.3.0
  - xorg-xproto=7.0.31
  - xz=5.2.5
  - zarr=2.6.1
  - zipp=3.4.0
  - zlib=1.2.11
  - zstd=1.4.5
  - pip:
    - aniso8601==8.1.0
    - attrs==20.3.0
    - bokeh==2.2.3
    - boto3==1.7.84
    - botocore==1.10.84
    - click==6.7
    - cloudpickle==1.6.0
    - colorcet==2.0.2
    - dask==2020.12.0
    - datashader==0.11.1
    - datashape==0.5.2
    - distributed==2020.12.0
    - fastcluster==1.1.26
    - fbpca==1.0
    - flask==1.0.4
    - flask-restful==0.3.8
    - flask-testing==0.7.1
    - fsspec==0.8.5
    - geosketch==1.1
    - harmonypy==0.0.5
    - heapdict==1.0.1
    - holoviews==1.14.0
    - idna==2.7
    - iniconfig==1.1.1
    - itsdangerous==1.1.0
    - jmespath==0.10.0
    - locket==0.2.0
    - markdown==3.3.3
    - msgpack==1.0.2
    - multipledispatch==0.6.0
    - obonet==0.2.6
    - panel==0.10.2
    - param==1.10.0
    - partd==1.1.0
    - pika==0.12.0
    - pluggy==0.13.1
    - psutil==5.8.0
    - py==1.9.0
    - pyct==0.4.8
    - pytest==6.1.2
    - pyviz-comms==0.7.6
    - pyyaml==5.1.2
    - requests==2.19.1
    - rpy2==3.0.5
    - s3transfer==0.1.13
    - simplegeneric==0.8.1
    - sortedcontainers==2.3.0
    - tblib==1.7.0
    - toolz==0.11.1
    - tqdm==4.32.2
    - typing-extensions==*******
    - urllib3==1.23
    - werkzeug==1.0.1
    - xarray==0.16.2
    - zict==2.0.0
prefix: ~/miniconda3/envs/scRNA_env
