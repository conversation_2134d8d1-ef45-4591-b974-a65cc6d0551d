#!/usr/bin/env python3
"""
Simplified CD4-PINNACLE Training Script
Based on the original PINNACLE framework, adapted for CD4 T cell subtypes
"""

import os
import time
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from datetime import datetime

# PINNACLE modules
from cd4_generate_input import read_cd4_data, get_metapaths, get_centerloss_labels
from center_loss import CenterLoss
import model as mdl
import utils

def set_random_seeds(seed=42):
    """Set random seeds for reproducibility"""
    import random
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)

class CD4PinnacleTrainer:
    def __init__(self, args):
        self.args = args
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"Using device: {self.device}")
        
        # Set random seeds
        set_random_seeds(args.seed)
        
        # Load data
        self.load_data()
        
        # Create model
        self.create_model()
        
        # Setup training
        self.setup_training()
    
    def load_data(self):
        """Load CD4 data"""
        print("Loading CD4 data...")
        self.data = read_cd4_data(
            global_ppi_f=self.args.global_ppi_file,
            ppi_dir=self.args.ppi_dir,
            mg_f=self.args.mg_file,
            feat_mat_dim=self.args.feat_mat_dim
        )
        
        self.metapaths = get_metapaths(self.data)
        print(f"Generated {len(self.metapaths)} metapaths")
    
    def create_model(self):
        """Create CD4-PINNACLE model"""
        print("Creating CD4-PINNACLE model...")
        
        # Model parameters
        num_ppi_relations = len(self.data['ppi_data'])
        num_mg_relations = 1  # Simplified for CD4
        
        print(f"Model config: {num_ppi_relations} PPI relations, {num_mg_relations} MG relations")
        
        # Create model
        self.model = mdl.Pinnacle(
            nfeat=self.args.feat_mat_dim,
            hidden=self.args.hidden_dim,
            output=self.args.output_dim,
            num_ppi_relations=num_ppi_relations,
            num_mg_relations=num_mg_relations,
            ppi_data=self.data['ppi_data'],
            n_heads=self.args.n_heads,
            pc_att_channels=self.args.pc_att_channels,
            dropout=self.args.dropout
        ).to(self.device)
        
        # Center loss for CD4 subtype separation
        self.center_loss = CenterLoss(
            num_classes=num_ppi_relations,
            feat_dim=self.args.output_dim * self.args.n_heads,
            use_gpu=(self.device.type == 'cuda')
        )
        
        print(f"Model created with {sum(p.numel() for p in self.model.parameters())} parameters")
    
    def setup_training(self):
        """Setup optimizers and loss functions"""
        self.optimizer = torch.optim.Adam(
            self.model.parameters(),
            lr=self.args.lr,
            weight_decay=self.args.weight_decay
        )
        
        self.optimizer_centloss = torch.optim.SGD(
            self.center_loss.parameters(),
            lr=self.args.lr_center
        )
        
        self.scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
            self.optimizer, 
            T_max=self.args.epochs, 
            eta_min=self.args.lr * 0.01
        )
    
    def training_step(self, epoch):
        """Single training step"""
        self.model.train()
        
        total_loss = 0
        link_losses = []
        center_losses = []
        
        # Process each CD4 subtype
        for context, ppi_data in self.data['ppi_data'].items():
            # Move data to device
            ppi_data = ppi_data.to(self.device)
            
            # Forward pass
            try:
                # Get embeddings
                embeddings = self.model.get_ppi_embeddings(ppi_data.x, context)
                
                # Link prediction loss
                pos_edge_index = ppi_data.edge_index[:, ppi_data.train_mask]
                pos_scores = self.model.predict_links(embeddings, pos_edge_index)
                
                # Negative sampling
                neg_edge_index = self.negative_sampling(
                    ppi_data.edge_index, 
                    ppi_data.x.size(0), 
                    num_neg_samples=pos_edge_index.size(1)
                )
                neg_scores = self.model.predict_links(embeddings, neg_edge_index)
                
                # Binary classification loss
                pos_labels = torch.ones(pos_scores.size(0), device=self.device)
                neg_labels = torch.zeros(neg_scores.size(0), device=self.device)
                
                link_loss = F.binary_cross_entropy_with_logits(
                    torch.cat([pos_scores, neg_scores]),
                    torch.cat([pos_labels, neg_labels])
                )
                
                # Center loss for subtype separation
                center_labels = torch.zeros(embeddings.size(0), dtype=torch.long, device=self.device)
                center_loss_val = self.center_loss(embeddings, center_labels)
                
                # Combined loss
                combined_loss = (
                    self.args.theta * link_loss + 
                    self.args.lambda_center * center_loss_val
                )
                
                # Backward pass
                self.optimizer.zero_grad()
                self.optimizer_centloss.zero_grad()
                combined_loss.backward()
                
                # Gradient clipping
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.args.grad_clip)
                
                self.optimizer.step()
                self.optimizer_centloss.step()
                
                total_loss += combined_loss.item()
                link_losses.append(link_loss.item())
                center_losses.append(center_loss_val.item())
                
            except Exception as e:
                print(f"Error processing {context}: {e}")
                continue
        
        # Update learning rate
        self.scheduler.step()
        
        return {
            'total_loss': total_loss / len(self.data['ppi_data']),
            'link_loss': np.mean(link_losses) if link_losses else 0,
            'center_loss': np.mean(center_losses) if center_losses else 0,
            'lr': self.scheduler.get_last_lr()[0]
        }
    
    def negative_sampling(self, edge_index, num_nodes, num_neg_samples):
        """Simple negative sampling"""
        # Generate random negative edges
        neg_edges = []
        existing_edges = set(map(tuple, edge_index.t().cpu().numpy()))
        
        while len(neg_edges) < num_neg_samples:
            i = torch.randint(0, num_nodes, (1,)).item()
            j = torch.randint(0, num_nodes, (1,)).item()
            if i != j and (i, j) not in existing_edges and (j, i) not in existing_edges:
                neg_edges.append([i, j])
        
        return torch.tensor(neg_edges, device=self.device).t()
    
    def train(self):
        """Main training loop"""
        print(f"\nStarting CD4-PINNACLE training for {self.args.epochs} epochs...")
        
        best_loss = float('inf')
        patience_counter = 0
        
        for epoch in range(self.args.epochs):
            epoch_start = time.time()
            
            # Training step
            metrics = self.training_step(epoch)
            
            epoch_time = time.time() - epoch_start
            
            # Logging
            if epoch % self.args.log_every == 0 or epoch == self.args.epochs - 1:
                print(f"Epoch {epoch+1:3d}/{self.args.epochs}: "
                      f"Loss={metrics['total_loss']:.4f} "
                      f"Link={metrics['link_loss']:.4f} "
                      f"Center={metrics['center_loss']:.4f} "
                      f"LR={metrics['lr']:.6f} "
                      f"Time={epoch_time:.1f}s")
            
            # Early stopping
            if metrics['total_loss'] < best_loss:
                best_loss = metrics['total_loss']
                patience_counter = 0
                
                # Save best model
                self.save_model(epoch, metrics, is_best=True)
            else:
                patience_counter += 1
            
            if patience_counter >= self.args.patience:
                print(f"Early stopping at epoch {epoch+1}")
                break
        
        print(f"\nTraining completed! Best loss: {best_loss:.4f}")
    
    def save_model(self, epoch, metrics, is_best=False):
        """Save model checkpoint"""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'center_loss_state_dict': self.center_loss.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'metrics': metrics,
            'args': self.args
        }
        
        # Save checkpoint
        checkpoint_path = os.path.join(self.args.save_dir, f'cd4_pinnacle_epoch_{epoch}.pth')
        torch.save(checkpoint, checkpoint_path)
        
        if is_best:
            best_path = os.path.join(self.args.save_dir, 'cd4_pinnacle_best.pth')
            torch.save(checkpoint, best_path)
            print(f"  Saved best model: {best_path}")
    
    def generate_embeddings(self):
        """Generate embeddings for all CD4 subtypes"""
        print("\nGenerating CD4 embeddings...")
        
        self.model.eval()
        cd4_embeddings = {}
        
        with torch.no_grad():
            for context, ppi_data in self.data['ppi_data'].items():
                ppi_data = ppi_data.to(self.device)
                embeddings = self.model.get_ppi_embeddings(ppi_data.x, context)
                cd4_embeddings[context] = embeddings.cpu()
                
                print(f"  {context}: {embeddings.shape}")
        
        # Save embeddings
        embeddings_path = os.path.join(self.args.save_dir, 'cd4_embeddings.pth')
        torch.save(cd4_embeddings, embeddings_path)
        print(f"Saved embeddings: {embeddings_path}")
        
        return cd4_embeddings

class Args:
    """Training arguments"""
    def __init__(self):
        # Data files
        self.global_ppi_file = "../data/networks/global_ppi_edgelist.txt"
        self.ppi_dir = "../data/networks/ppi_edgelists/"
        self.mg_file = "../data/networks/cd4_mg_edgelist.txt"
        
        # Model parameters
        self.feat_mat_dim = 2048
        self.hidden_dim = 64
        self.output_dim = 32
        self.n_heads = 8
        self.pc_att_channels = 16
        self.dropout = 0.3
        
        # Training parameters
        self.epochs = 200
        self.lr = 0.001
        self.weight_decay = 5e-4
        self.lr_center = 0.01
        self.grad_clip = 1.0
        
        # Loss weights
        self.theta = 0.1  # Link prediction loss weight
        self.lambda_center = 0.01  # Center loss weight
        
        # Training control
        self.patience = 20
        self.log_every = 10
        self.seed = 42
        
        # Output
        self.save_dir = "../results/cd4_pinnacle"
        os.makedirs(self.save_dir, exist_ok=True)

def main():
    """Main training function"""
    print("="*70)
    print("CD4-PINNACLE: Context-Aware Protein Embeddings for CD4 T Cells")
    print("="*70)
    
    # Setup arguments
    args = Args()
    
    # Create trainer
    trainer = CD4PinnacleTrainer(args)
    
    # Train model
    trainer.train()
    
    # Generate embeddings
    embeddings = trainer.generate_embeddings()
    
    print("\nCD4-PINNACLE training completed successfully!")
    return trainer, embeddings

if __name__ == "__main__":
    trainer, embeddings = main()