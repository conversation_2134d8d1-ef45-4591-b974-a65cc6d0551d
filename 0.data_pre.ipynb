{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "shellscript"}}, "outputs": [], "source": ["python 0.constructPPI.py \\\n", "    -rank True \\\n", "    -annotation annotation_correction_low \\\n", "    -rank_pval_filename \"/mnt/f/文献汇报/21_PINNACLE/T细胞图谱/CD4_PINNACLE_project/data/raw/ranked_CD4\" \\\n", "    -subsample True \\\n", "    -num_cells_cutoff 200 \\\n", "    -iterations 10\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "shellscript"}}, "outputs": [], "source": ["python 0.constructPPI.py \\\n", "    -rank False \\\n", "    -annotation annotation_correction_low \\\n", "    -rank_pval_filename \"/mnt/f/文献汇报/21_PINNACLE/T细胞图谱/CD4_PINNACLE_project/data/raw/ranked_CD4\" \\\n", "    -max_pval 1.0 \\\n", "    -max_num_genes 4000 \\\n", "    -subsample True\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["第一个命令 (基因排序)：\n", "\n", "-rank True: 指示脚本执行基因排序模式。\n", "-annotation annotation_low: 指定细胞类型注释所在的列名为 annotation_low。\n", "-rank_pval_filename /mnt/f/文献汇报/21_PINNACLE/T细胞图谱/data/ranked_CD4: 设置基因排序结果文件的名称前缀。\n", "-subsample True: 启用细胞二次采样。\n", "-num_cells_cutoff 100: 每个细胞类型随机抽取 100 个细胞。\n", "-iterations 10: 重复二次采样 10 次。\n", "第二个命令 (提取PPI网络)：\n", "\n", "-rank False: 指示脚本执行提取PPI网络模式。\n", "-annotation annotation_low: 指定细胞类型注释所在的列名为 annotation_low。\n", "-rank_pval_filename /mnt/f/文献汇报/21_PINNACLE/T细胞图谱/data/ranked_CD4: 指定基因排序结果文件的前缀。\n", "-celltype_ppi_filename /mnt/f/文献汇报/21_PINNACLE/T细胞图谱/data/ppi_CD4: 设置细胞类型特异性PPI网络文件的名称前缀。\n", "-max_pval 1.0: p-value 阈值设为 1.0 (保留所有基因)。\n", "-max_num_genes 4000: 最多保留 4000 个基因 (在LCC计算之前)。\n", "-subsample True: 指示使用了二次采样的基因排序结果。\n", "运行流程：\n", "\n", "首先运行第一个命令进行基因排序，这将生成 ranked_CD4_0.csv 到 ranked_CD4_9.csv 等文件。\n", "然后运行第二个命令，它会读取这些排序文件，根据设定的阈值筛选基因，提取PPI网络，并将结果保存在 ppi_CD4_maxpval=1.0.csv，同时生成每个细胞类型的 edgelist 文件。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31mRunning cells with 'scRNA_env (Python 3.8.2)' requires the ipykernel package.\n", "\u001b[1;31mRun the following command to install 'ipykernel' into the Python environment. \n", "\u001b[1;31mCommand: 'conda install -n scRNA_env ipykernel --update-deps --force-reinstall'"]}], "source": ["import scanpy as sc\n", "adata = sc.read_h5ad(\"/mnt/f/文献汇报/21_PINNACLE/T细胞图谱/CD4_PINNACLE_project/data/raw/ranked_CD4_1.h5ad\")\n", "adata"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "scRNA_env", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.8.2"}}, "nbformat": 4, "nbformat_minor": 2}