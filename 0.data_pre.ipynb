{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "shellscript"}}, "outputs": [], "source": ["python 0.constructPPI.py \\\n", "    -rank True \\\n", "    -annotation annotation_correction_low \\\n", "    -rank_pval_filename \"/mnt/f/文献汇报/21_PINNACLE/T细胞图谱/CD4_PINNACLE_project/data/raw/ranked_CD4\" \\\n", "    -subsample True \\\n", "    -num_cells_cutoff 200 \\\n", "    -iterations 10\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "shellscript"}}, "outputs": [], "source": ["python 0.constructPPI.py \\\n", "    -rank False \\\n", "    -annotation annotation_correction_low \\\n", "    -rank_pval_filename \"/mnt/f/文献汇报/21_PINNACLE/T细胞图谱/CD4_PINNACLE_project/data/raw/ranked_CD4\" \\\n", "    -max_pval 1.0 \\\n", "    -max_num_genes 4000 \\\n", "    -subsample True\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["第一个命令 (基因排序)：\n", "\n", "-rank True: 指示脚本执行基因排序模式。\n", "-annotation annotation_low: 指定细胞类型注释所在的列名为 annotation_low。\n", "-rank_pval_filename /mnt/f/文献汇报/21_PINNACLE/T细胞图谱/data/ranked_CD4: 设置基因排序结果文件的名称前缀。\n", "-subsample True: 启用细胞二次采样。\n", "-num_cells_cutoff 100: 每个细胞类型随机抽取 100 个细胞。\n", "-iterations 10: 重复二次采样 10 次。\n", "第二个命令 (提取PPI网络)：\n", "\n", "-rank False: 指示脚本执行提取PPI网络模式。\n", "-annotation annotation_low: 指定细胞类型注释所在的列名为 annotation_low。\n", "-rank_pval_filename /mnt/f/文献汇报/21_PINNACLE/T细胞图谱/data/ranked_CD4: 指定基因排序结果文件的前缀。\n", "-celltype_ppi_filename /mnt/f/文献汇报/21_PINNACLE/T细胞图谱/data/ppi_CD4: 设置细胞类型特异性PPI网络文件的名称前缀。\n", "-max_pval 1.0: p-value 阈值设为 1.0 (保留所有基因)。\n", "-max_num_genes 4000: 最多保留 4000 个基因 (在LCC计算之前)。\n", "-subsample True: 指示使用了二次采样的基因排序结果。\n", "运行流程：\n", "\n", "首先运行第一个命令进行基因排序，这将生成 ranked_CD4_0.csv 到 ranked_CD4_9.csv 等文件。\n", "然后运行第二个命令，它会读取这些排序文件，根据设定的阈值筛选基因，提取PPI网络，并将结果保存在 ppi_CD4_maxpval=1.0.csv，同时生成每个细胞类型的 edgelist 文件。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31mRunning cells with 'scRNA_env (Python 3.8.2)' requires the ipykernel package.\n", "\u001b[1;31mRun the following command to install 'ipykernel' into the Python environment. \n", "\u001b[1;31mCommand: 'conda install -n scRNA_env ipykernel --update-deps --force-reinstall'"]}], "source": ["import scanpy as sc\n", "adata = sc.read_h5ad(\"/mnt/f/文献汇报/21_PINNACLE/T细胞图谱/CD4_PINNACLE_project/data/raw/ranked_CD4_1.h5ad\")\n", "adata"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "latex"}}, "outputs": [], "source": ["# prompt\n", "You are a bioinformatics and computational biology expert with 20 years of experience. This project folder contains a published open-source project with complete documentation including README files, data preprocessing scripts, model code, training and fine-tuning implementations.\n", "\n", "**Project Goal**: Based on the existing PINNACLE model, redesign and develop a new research project using CD4 T cell single-cell data located in `/data/raw/CD4.h5ad`.\n", "\n", "**Specific Requirements**:\n", "1. **Literature Review & Design Enhancement**: \n", "   - Read and analyze the preliminary research design document: \"CD4T_project.md\"\n", "   - Enhance and refine this preliminary design with detailed improvements and professional polish\n", "   - Integrate insights from the PINNACLE model's methodology and capabilities\n", "\n", "2. **Technical Implementation**:\n", "   - Analyze the PINNACLE model's detailed documentation and source code\n", "   - Load and examine the `/data/raw/CD4.h5ad` file programmatically\n", "   - Use the \"annotation_correction_low\" column as the authoritative cell type annotation (ignore other celltype columns)\n", "   - The preprocessing virtual environment is already configured and ready to use\n", "\n", "3. **Execution Approach**:\n", "   - Follow a step-by-step methodology with clear reasoning at each stage\n", "   - Use systematic systematic thinking and analysis throughout the process\n", "   - Provide detailed explanations for each decision and implementation choice\n", "   - Ensure reproducibility and scientific rigor in all analyses\n", "\n", "4. **Deliverables**:\n", "   - Enhanced research design document with scientific justification\n", "   - Code implementation for data analysis using PINNACLE framework\n", "   - Clear documentation of methodology and results\n", "   - Recommendations for next steps in the research pipeline\n", "\n", "Please begin by exploring the codebase structure, understanding the PINNACLE model architecture, and then proceed with the systematic analysis and enhancement of the research design."]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "latex"}}, "outputs": [], "source": ["作为一位拥有20年生物信息学和计算生物学经验的专家，请基于当前CD4-PINNACLE项目的背景，对PINNACLE模型进行深入的技术分析和改进建议。具体要求如下：\n", "\n", "**分析维度：**\n", "1. **模型架构层面**：分析PINNACLE的几何深度学习架构、PCTConv和PPIConv层设计、多头注意力机制的优缺点\n", "2. **数据处理层面**：评估当前基于Tabula Sapiens（156种细胞类型）的局限性，以及适配CD4 T细胞高精度分类（42个亚型）的挑战\n", "3. **训练策略层面**：分析现有的损失函数组合（链接预测损失+中心损失）、批处理策略、负采样方法的有效性\n", "4. **生物学解释性**：评估模型在捕获CD4 T细胞亚型特异性蛋白质功能方面的能力和局限性\n", "5. **计算效率层面**：分析模型在处理大规模CD4数据（可能超过100万细胞）时的计算瓶颈\n", "\n", "**改进建议要求：**\n", "- 针对CD4 T细胞的特殊生物学特性提出具体的模型架构改进\n", "- 考虑疾病情境（如肿瘤免疫、自身免疫）的建模需求\n", "- 提出提高模型可解释性的具体方法\n", "- 建议优化训练效率和内存使用的技术方案\n", "- 考虑与现有T细胞生物学知识（如转录因子调控、代谢途径）的整合方式\n", "\n", "**输出格式：**\n", "请按照\"现状分析 → 问题识别 → 改进方案 → 实施建议\"的逻辑结构，提供详细的技术分析报告。请始终保持ultrathinking模式"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "shellscript"}}, "outputs": [], "source": ["python data_prep/cd4_constructPPI.py \\\n", "    -rank True \\\n", "    -rank_pval_filename \"/mnt/f/文献汇报/21_PINNACLE/T细胞图谱/CD4_PINNACLE_project/networks/cd4_ranked_genes\" \\\n", "    -annotation \"annotation_correction_low\" \\\n", "    -min_cells 100 \\\n", "    -method \"wilcoxon\" \\\n", "    -subsample False"]}], "metadata": {"kernelspec": {"display_name": "scRNA_env", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.8.2"}}, "nbformat": 4, "nbformat_minor": 2}