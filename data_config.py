############################################################
# DATA DIRECTORY

DATA_DIR = "/mnt/f/文献汇报/21_PINNACLE/T细胞图谱/CD4_PINNACLE_project/data/"                                       # Data directory
RAW_DATA_DIR = DATA_DIR + "raw/"                            # Directory with all draw data
OUTPUT_DIR = DATA_DIR + "networks/"                         # Directory with all networks


############################################################
# DATA FILES

# Single cell transcriptomic atlas
TABULA_SAPIENS_DIR = RAW_DATA_DIR + "CD4_subsample.h5ad"    # CD4 T cell single-cell atlas (subsampled for memory efficiency)
TS_TISSUE_DATA_DIR = OUTPUT_DIR + "cd4_tissue_data.csv"      # Generated data_prep/presave_compartments.py

# CD4 T cell specific data files
CD4_RANKED_GENES_DIR = OUTPUT_DIR + "cd4_ranked_genes.csv"   # Ranked genes per CD4 subtype
CD4_CELLTYPE_PPI_DIR = OUTPUT_DIR + "cd4_celltype_ppi.csv"   # CD4 subtype-specific PPI networks
CELLPHONEDB_OUTPUT_DIR = OUTPUT_DIR + "cellphonedb/"         # CellPhoneDB results
CCI_EDGELIST_DIR = OUTPUT_DIR + "cci_edgelist.txt"          # Cell-cell interaction network

# Global PPI network
PPI_DIR = OUTPUT_DIR + "global_ppi_edgelist.txt"            # Global PPI network
METAGRAPH_DIR = OUTPUT_DIR + "mg_edgelist.txt"              # Metagraph

############################################################
# AUXILIARY DATA FILES

CELL_ONTOLOGY_DIR = DATA_DIR + "cell-ontology/cl-full.obo"  # Cell ontology
BTO_DIR = RAW_DATA_DIR + "BTO.obo"                          # Tissue ontology