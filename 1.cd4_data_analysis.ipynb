{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# CD4 T Cell Data Analysis for PINNACLE Project\n", "\n", "This notebook provides comprehensive analysis of the CD4 T cell single-cell RNA-seq dataset that will be used to build the CD4-PINNACLE model.\n", "\n", "## Objectives:\n", "1. Analyze the structure and composition of CD4.h5ad\n", "2. Examine the 42 CD4 T cell subtypes from annotation_correction_low\n", "3. Assess data quality and preprocessing requirements\n", "4. Visualize subtype distributions and relationships\n", "5. Identify key biological features for contextualized PPI network construction"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import scanpy as sc\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from collections import Counter\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Configure scanpy\n", "sc.settings.verbosity = 3\n", "sc.settings.set_figure_params(dpi=80, facecolor='white')\n", "\n", "# Load data config\n", "import sys\n", "sys.path.append('.')\n", "from data_config import TABULA_SAPIENS_DIR, OUTPUT_DIR"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Data Loading and Basic Statistics"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load the CD4 T cell dataset\n", "print(f\"Loading CD4 data from: {TABULA_SAPIENS_DIR}\")\n", "adata = sc.read_h5ad(TABULA_SAPIENS_DIR)\n", "\n", "print(f\"\\n=== CD4 Dataset Overview ===\")\n", "print(f\"Shape: {adata.shape}\")\n", "print(f\"Cells: {adata.n_obs:,}\")\n", "print(f\"Genes: {adata.n_vars:,}\")\n", "print(f\"Memory usage: {adata.X.nbytes / 1e9:.2f} GB\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Examine available annotations\n", "print(\"\\n=== Available Cell Type Annotations ===\")\n", "celltype_cols = [col for col in adata.obs.columns if 'celltype' in col.lower() or 'annotation' in col.lower()]\n", "for col in celltype_cols:\n", "    n_unique = adata.obs[col].nunique()\n", "    print(f\"{col}: {n_unique} unique categories\")\n", "    if n_unique <= 10:\n", "        print(f\"  Categories: {list(adata.obs[col].unique())}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. CD4 T Cell Subtype Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Focus on annotation_correction_low - our target annotation\n", "target_annotation = 'annotation_correction_low'\n", "\n", "if target_annotation in adata.obs.columns:\n", "    subtype_counts = adata.obs[target_annotation].value_counts()\n", "    print(f\"\\n=== CD4 T Cell Subtypes ({target_annotation}) ===\")\n", "    print(f\"Total subtypes: {len(subtype_counts)}\")\n", "    print(f\"\\nTop 10 most abundant subtypes:\")\n", "    print(subtype_counts.head(10))\n", "    \n", "    # Calculate basic statistics\n", "    print(f\"\\n=== Subtype Statistics ===\")\n", "    print(f\"Mean cells per subtype: {subtype_counts.mean():.0f}\")\n", "    print(f\"Median cells per subtype: {subtype_counts.median():.0f}\")\n", "    print(f\"Min cells per subtype: {subtype_counts.min()}\")\n", "    print(f\"Max cells per subtype: {subtype_counts.max()}\")\n", "    \n", "    # Identify rare subtypes (< 1000 cells)\n", "    rare_subtypes = subtype_counts[subtype_counts < 1000]\n", "    print(f\"\\nRare subtypes (< 1000 cells): {len(rare_subtypes)}\")\n", "    if len(rare_subtypes) > 0:\n", "        print(rare_subtypes)\n", "else:\n", "    print(f\"Warning: {target_annotation} not found in data!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize subtype distribution\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "\n", "# 1. Bar plot of top 20 subtypes\n", "top_20 = subtype_counts.head(20)\n", "ax1 = axes[0, 0]\n", "top_20.plot(kind='bar', ax=ax1, color='skyblue')\n", "ax1.set_title('Top 20 CD4 T Cell Subtypes', fontsize=14, fontweight='bold')\n", "ax1.set_xlabel('CD4 Subtype')\n", "ax1.set_ylabel('Number of Cells')\n", "ax1.tick_params(axis='x', rotation=45)\n", "\n", "# 2. Histogram of subtype sizes\n", "ax2 = axes[0, 1]\n", "ax2.hist(subtype_counts.values, bins=20, color='lightcoral', alpha=0.7)\n", "ax2.set_title('Distribution of Subtype Sizes', fontsize=14, fontweight='bold')\n", "ax2.set_xlabel('Number of Cells per Subtype')\n", "ax2.set_ylabel('Frequency')\n", "ax2.axvline(subtype_counts.mean(), color='red', linestyle='--', label=f'Mean: {subtype_counts.mean():.0f}')\n", "ax2.legend()\n", "\n", "# 3. Cumulative distribution\n", "ax3 = axes[1, 0]\n", "cumulative_pct = (subtype_counts.cumsum() / subtype_counts.sum() * 100)\n", "ax3.plot(range(1, len(cumulative_pct) + 1), cumulative_pct, marker='o', color='green')\n", "ax3.set_title('Cumulative Cell Distribution', fontsize=14, fontweight='bold')\n", "ax3.set_xlabel('Subtype Rank')\n", "ax3.set_ylabel('Cumulative Percentage of Cells')\n", "ax3.grid(True, alpha=0.3)\n", "\n", "# 4. Pie chart of functional categories\n", "ax4 = axes[1, 1]\n", "# Extract functional categories from subtype names\n", "functional_categories = {}\n", "for subtype in subtype_counts.index:\n", "    if 'Tn' in subtype:\n", "        category = 'Naive (Tn)'\n", "    elif 'Treg' in subtype:\n", "        category = 'Regulatory (Treg)'\n", "    elif 'Th17' in subtype:\n", "        category = 'Th17'\n", "    elif 'Th1' in subtype:\n", "        category = 'Th1'\n", "    elif 'Tfh' in subtype:\n", "        category = 'Tfh'\n", "    elif 'Tcm' in subtype:\n", "        category = 'Central Memory (Tcm)'\n", "    elif 'Tem' in subtype:\n", "        category = 'Effector Memory (Tem)'\n", "    elif '<PERSON>' in subtype:\n", "        category = 'Exhausted (Tex)'\n", "    elif 'Trm' in subtype:\n", "        category = 'Tissue Resident (Trm)'\n", "    elif 'Tstr' in subtype:\n", "        category = 'Stressed (Tstr)'\n", "    elif 'Tisg' in subtype:\n", "        category = 'ISG+ (Tisg)'\n", "    elif 'Cytotoxicity' in subtype:\n", "        category = 'Cytotoxic'\n", "    else:\n", "        category = 'Other'\n", "    \n", "    functional_categories[category] = functional_categories.get(category, 0) + subtype_counts[subtype]\n", "\n", "ax4.pie(functional_categories.values(), labels=functional_categories.keys(), autopct='%1.1f%%', startangle=90)\n", "ax4.set_title('Functional Categories Distribution', fontsize=14, fontweight='bold')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Save functional categories for later use\n", "print(\"\\n=== Functional Categories ===\")\n", "for category, count in sorted(functional_categories.items(), key=lambda x: x[1], reverse=True):\n", "    print(f\"{category}: {count:,} cells ({count/sum(functional_categories.values())*100:.1f}%)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Data Quality Assessment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Basic QC metrics\n", "print(\"=== Data Quality Metrics ===\")\n", "\n", "# Check for basic QC columns\n", "qc_cols = ['total_counts', 'n_genes_by_counts', 'pct_counts_mt']\n", "available_qc = [col for col in qc_cols if col in adata.obs.columns]\n", "print(f\"Available QC metrics: {available_qc}\")\n", "\n", "# Display QC statistics\n", "if available_qc:\n", "    qc_stats = adata.obs[available_qc].describe()\n", "    print(\"\\nQC Statistics:\")\n", "    print(qc_stats)\n", "\n", "# Check gene expression statistics\n", "print(f\"\\n=== Gene Expression Statistics ===\")\n", "print(f\"Total UMI counts: {adata.X.sum():,.0f}\")\n", "print(f\"Mean UMI per cell: {adata.X.sum(axis=1).mean():.0f}\")\n", "print(f\"Mean genes per cell: {(adata.X > 0).sum(axis=1).mean():.0f}\")\n", "print(f\"Sparsity: {(adata.X == 0).sum() / adata.X.size * 100:.1f}%\")\n", "\n", "# Check for highly variable genes\n", "if 'highly_variable' in adata.var.columns:\n", "    n_hvg = adata.var['highly_variable'].sum()\n", "    print(f\"Highly variable genes: {n_hvg} ({n_hvg/adata.n_vars*100:.1f}%)\")\n", "else:\n", "    print(\"No highly variable genes identified yet\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize QC metrics\n", "if len(available_qc) >= 2:\n", "    fig, axes = plt.subplots(1, min(3, len(available_qc)), figsize=(15, 5))\n", "    if len(available_qc) == 1:\n", "        axes = [axes]\n", "    \n", "    for i, qc_col in enumerate(available_qc[:3]):\n", "        ax = axes[i] if len(available_qc) > 1 else axes[0]\n", "        \n", "        # Create violin plot by subtype for top 10 subtypes\n", "        top_subtypes = subtype_counts.head(10).index\n", "        subset_data = adata[adata.obs[target_annotation].isin(top_subtypes)]\n", "        \n", "        violin_data = []\n", "        labels = []\n", "        for subtype in top_subtypes:\n", "            subtype_cells = subset_data[subset_data.obs[target_annotation] == subtype]\n", "            if qc_col in subtype_cells.obs.columns:\n", "                violin_data.append(subtype_cells.obs[qc_col].values)\n", "                labels.append(subtype.replace('CD4 ', ''))\n", "        \n", "        if violin_data:\n", "            ax.violinplot(violin_data, positions=range(len(violin_data)), showmeans=True)\n", "            ax.set_xticks(range(len(labels)))\n", "            ax.set_xticklabels(labels, rotation=45, ha='right')\n", "            ax.set_title(f'{qc_col} by Top CD4 Subtypes', fontweight='bold')\n", "            ax.set_ylabel(qc_col)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Sample and Project Distribution"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze sample and project distribution\n", "print(\"=== Sample and Project Distribution ===\")\n", "\n", "if 'Sample' in adata.obs.columns:\n", "    n_samples = adata.obs['Sample'].nunique()\n", "    print(f\"Number of samples: {n_samples}\")\n", "    \n", "    # Sample distribution\n", "    sample_counts = adata.obs['Sample'].value_counts()\n", "    print(f\"\\nSample sizes:\")\n", "    print(f\"  Mean: {sample_counts.mean():.0f}\")\n", "    print(f\"  Median: {sample_counts.median():.0f}\")\n", "    print(f\"  Range: {sample_counts.min()} - {sample_counts.max()}\")\n", "\n", "if 'Project' in adata.obs.columns:\n", "    n_projects = adata.obs['Project'].nunique()\n", "    print(f\"\\nNumber of projects: {n_projects}\")\n", "    \n", "    # Project distribution\n", "    project_counts = adata.obs['Project'].value_counts()\n", "    print(f\"\\nProject sizes:\")\n", "    print(project_counts.head(10))\n", "\n", "# Cross-tabulation of subtypes by project (if available)\n", "if 'Project' in adata.obs.columns and target_annotation in adata.obs.columns:\n", "    print(\"\\n=== Subtype Distribution Across Projects ===\")\n", "    crosstab = pd.crosstab(adata.obs[target_annotation], adata.obs['Project'])\n", "    \n", "    # Show distribution for top 10 subtypes and projects\n", "    top_subtypes = subtype_counts.head(10).index\n", "    top_projects = project_counts.head(5).index\n", "    \n", "    subset_crosstab = crosstab.loc[top_subtypes, top_projects]\n", "    print(subset_crosstab)\n", "    \n", "    # Visualize as heatmap\n", "    plt.figure(figsize=(12, 8))\n", "    sns.heatmap(subset_crosstab, annot=True, fmt='d', cmap='YlOrRd', \n", "                xticklabels=True, yticklabels=True)\n", "    plt.title('CD4 Subtype Distribution Across Top Projects', fontweight='bold')\n", "    plt.xlabel('Project')\n", "    plt.ylabel('CD4 Subtype')\n", "    plt.xticks(rotation=45, ha='right')\n", "    plt.yticks(rotation=0)\n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Gene Expression Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze key T cell marker genes\n", "print(\"=== Key T Cell Marker Gene Analysis ===\")\n", "\n", "# Define key CD4 T cell markers\n", "cd4_markers = {\n", "    'General CD4': ['CD4', 'CD3D', 'CD3E', 'CD3G'],\n", "    'Naive': ['CCR7', 'SELL', 'TCF7', 'LEF1'],\n", "    'Th1': ['IFNG', 'TBX21', 'CXCR3'],\n", "    'Th17': ['IL17A', 'IL17F', 'RORC', 'CCR6'],\n", "    'Treg': ['FOXP3', 'IL2RA', 'IKZF2', 'CTLA4'],\n", "    'Tfh': ['CXCR5', 'BCL6', 'PDCD1', 'IL21'],\n", "    'Memory': ['CD44', 'CD69', 'CD25'],\n", "    'Exhausted': ['PDCD1', 'CTLA4', 'TIGIT', 'LAG3', 'HAVCR2'],\n", "    'Cytotoxic': ['PRF1', 'GZMA', 'GZMB', 'NKG7']\n", "}\n", "\n", "# Check which markers are available\n", "available_markers = {}\n", "for category, genes in cd4_markers.items():\n", "    available_genes = [g for g in genes if g in adata.var.index]\n", "    if available_genes:\n", "        available_markers[category] = available_genes\n", "        print(f\"{category}: {len(available_genes)}/{len(genes)} genes available\")\n", "        print(f\"  Available: {available_genes}\")\n", "        missing = [g for g in genes if g not in adata.var.index]\n", "        if missing:\n", "            print(f\"  Missing: {missing}\")\n", "    else:\n", "        print(f\"{category}: No genes available\")\n", "    print()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate and visualize marker gene expression by subtype\n", "if available_markers:\n", "    print(\"=== Marker Gene Expression by Subtype ===\")\n", "    \n", "    # Focus on top 15 subtypes for visualization\n", "    top_subtypes = subtype_counts.head(15).index\n", "    \n", "    # Calculate mean expression for each marker in each subtype\n", "    marker_expression = {}\n", "    \n", "    for category, genes in available_markers.items():\n", "        category_expr = []\n", "        for subtype in top_subtypes:\n", "            subtype_cells = adata[adata.obs[target_annotation] == subtype]\n", "            \n", "            # Calculate mean expression for this category\n", "            if len(genes) > 0:\n", "                gene_expr = []\n", "                for gene in genes:\n", "                    if gene in subtype_cells.var.index:\n", "                        expr = subtype_cells[:, gene].X.mean()\n", "                        gene_expr.append(expr)\n", "                \n", "                if gene_expr:\n", "                    category_expr.append(np.mean(gene_expr))\n", "                else:\n", "                    category_expr.append(0)\n", "            else:\n", "                category_expr.append(0)\n", "        \n", "        marker_expression[category] = category_expr\n", "    \n", "    # Create heatmap of marker expression\n", "    if marker_expression:\n", "        expr_df = pd.DataFrame(marker_expression, index=[s.replace('CD4 ', '') for s in top_subtypes])\n", "        \n", "        plt.figure(figsize=(12, 10))\n", "        sns.heatmap(expr_df.T, annot=True, fmt='.2f', cmap='RdYlBu_r', \n", "                    xticklabels=True, yticklabels=True, cbar_kws={'label': 'Mean Expression'})\n", "        plt.title('CD4 T Cell Marker Expression by Subtype', fontweight='bold')\n", "        plt.xlabel('CD4 Subtype')\n", "        plt.ylabel('Marker Category')\n", "        plt.xticks(rotation=45, ha='right')\n", "        plt.yticks(rotation=0)\n", "        plt.tight_layout()\n", "        plt.show()\n", "        \n", "        # Print top expressing subtypes for each category\n", "        print(\"\\n=== Top Expressing Subtypes by Marker Category ===\")\n", "        for category in expr_df.columns:\n", "            top_expr = expr_df[category].nlargest(3)\n", "            print(f\"{category}:\")\n", "            for subtype, expr in top_expr.items():\n", "                print(f\"  {subtype}: {expr:.3f}\")\n", "            print()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Prepare Summary for PINNACLE Training"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create summary statistics for PINNACLE training\n", "print(\"=== CD4-PINNACLE Training Summary ===\")\n", "\n", "# Filter subtypes with sufficient cells (>= 100 cells as per PINNACLE default)\n", "min_cells = 100\n", "valid_subtypes = subtype_counts[subtype_counts >= min_cells]\n", "\n", "print(f\"\\nSubtypes with >= {min_cells} cells: {len(valid_subtypes)}/{len(subtype_counts)}\")\n", "print(f\"Total cells in valid subtypes: {valid_subtypes.sum():,} ({valid_subtypes.sum()/subtype_counts.sum()*100:.1f}%)\")\n", "\n", "# Summary statistics\n", "summary_stats = {\n", "    'total_cells': adata.n_obs,\n", "    'total_genes': adata.n_vars,\n", "    'total_subtypes': len(subtype_counts),\n", "    'valid_subtypes': len(valid_subtypes),\n", "    'cells_in_valid_subtypes': valid_subtypes.sum(),\n", "    'mean_cells_per_subtype': valid_subtypes.mean(),\n", "    'median_cells_per_subtype': valid_subtypes.median(),\n", "    'min_cells_per_subtype': valid_subtypes.min(),\n", "    'max_cells_per_subtype': valid_subtypes.max(),\n", "    'annotation_column': target_annotation\n", "}\n", "\n", "print(\"\\n=== Summary Statistics ===\")\n", "for key, value in summary_stats.items():\n", "    if isinstance(value, (int, float)) and value > 1000:\n", "        print(f\"{key}: {value:,.0f}\")\n", "    else:\n", "        print(f\"{key}: {value}\")\n", "\n", "# Save summary for later use\n", "summary_df = pd.DataFrame([summary_stats])\n", "summary_df.to_csv(OUTPUT_DIR + 'cd4_dataset_summary.csv', index=False)\n", "\n", "# Save valid subtypes list\n", "valid_subtypes_df = pd.DataFrame({\n", "    'subtype': valid_subtypes.index,\n", "    'cell_count': valid_subtypes.values\n", "})\n", "valid_subtypes_df.to_csv(OUTPUT_DIR + 'cd4_valid_subtypes.csv', index=False)\n", "\n", "print(f\"\\nSummary saved to: {OUTPUT_DIR}cd4_dataset_summary.csv\")\n", "print(f\"Valid subtypes saved to: {OUTPUT_DIR}cd4_valid_subtypes.csv\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create final recommendations for PINNACLE training\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"CD4-<PERSON><PERSON><PERSON><PERSON><PERSON> TRAINING RECOMMENDATIONS\")\n", "print(\"=\"*60)\n", "\n", "print(f\"\\n1. DATASET CHARACTERISTICS:\")\n", "print(f\"   - Total CD4 T cells: {adata.n_obs:,}\")\n", "print(f\"   - CD4 subtypes for training: {len(valid_subtypes)}\")\n", "print(f\"   - Primary annotation: {target_annotation}\")\n", "print(f\"   - Recommended minimum cells per subtype: {min_cells}\")\n", "\n", "print(f\"\\n2. MAJOR FUNCTIONAL GROUPS:\")\n", "for category, count in sorted(functional_categories.items(), key=lambda x: x[1], reverse=True)[:5]:\n", "    print(f\"   - {category}: {count:,} cells\")\n", "\n", "print(f\"\\n3. PREPROCESSING RECOMMENDATIONS:\")\n", "print(f\"   - Use annotation_correction_low as cell type labels\")\n", "print(f\"   - Apply minimum cell count filter: {min_cells} cells per subtype\")\n", "print(f\"   - Expected PPI networks: {len(valid_subtypes)} context-specific networks\")\n", "print(f\"   - Recommended max_genes for PPI construction: 3000-4000\")\n", "\n", "print(f\"\\n4. EXPECTED OUTPUTS:\")\n", "print(f\"   - Context-aware protein embeddings for {len(valid_subtypes)} CD4 subtypes\")\n", "print(f\"   - Enhanced resolution for T cell-specific therapeutic targets\")\n", "print(f\"   - Improved understanding of CD4 subtype-specific protein interactions\")\n", "\n", "print(f\"\\n5. NEXT STEPS:\")\n", "print(f\"   - Run data_prep/0.constructPPI.py with annotation_correction_low\")\n", "print(f\"   - Proceed with CellPhoneDB analysis for cell-cell interactions\")\n", "print(f\"   - Train CD4-PINNACLE model with generated networks\")\n", "print(f\"   - Evaluate on CD4-specific therapeutic target prioritization\")\n", "\n", "print(\"\\n\" + \"=\"*60)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.13"}}, "nbformat": 4, "nbformat_minor": 4}