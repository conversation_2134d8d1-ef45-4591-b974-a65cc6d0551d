
### **新课题：基于T细胞高精度情境化图谱的蛋白质功能深度学习模型，赋能肿瘤免疫和自身免疫疾病的精准靶点发现**

**1. 详细研究背景**

蛋白质是细胞的功能单位，其相互作用决定了多种生物功能。理解蛋白质功能并开发分子疗法需要明确蛋白质在何种细胞类型中发挥作用以及蛋白质之间的相互作用。然而，现有算法在模拟跨生物情境的蛋白质相互作用方面仍面临挑战。传统的蛋白质表征学习方法通常是“情境无关”的，即每个蛋白质只有一个单一的、整合了所有情境的表征，这无法识别蛋白质在不同细胞类型和疾病状态下可能存在的不同功能，从而阻碍了多效性（pleiotropy）和细胞类型特异性蛋白质作用的预测。

**PINNACLE**（Protein Network-based Algorithm for Contextual Learning）正是在这一背景下被提出的一种**几何深度学习方法**，旨在生成**情境感知蛋白质表示（context-aware protein representations）**。该模型通过整合多器官单细胞图谱（如Tabula Sapiens，包含24个组织的156种细胞类型）和情境化蛋白质相互作用网络，为每个蛋白质在特定的细胞类型情境下生成独特的表示。PINNACLE能够生成394,760个情境化蛋白质表示，其中每个蛋白质表示都具有细胞类型特异性。PINNACLE的嵌入空间能够反映细胞和组织结构，并能在下游任务中表现出色，例如，它能增强基于3D结构的蛋白质相互作用预测，并揭示药物在不同细胞类型中的作用。在风湿性关节炎（RA）和炎症性肠病（IBD）的治疗靶点提名中，PINNACLE的性能优于现有模型，并能识别出具有更高预测能力的细胞类型情境。

尽管PINNACLE能力强大，但它所依赖的单细胞转录组图谱（Tabula Sapiens）主要来自健康人样本。其包含的156种细胞类型，在高度异质的免疫细胞群体，尤其是T细胞这样复杂的细胞类型中，可能无法捕捉到足够细致的、疾病特异性的亚型和状态。例如，PINNACLE的原始研究中明确指出，Tabula Sapiens不包含与RA疾病进展相关的滑膜组织。PINNACLE文献本身也建议，通过在疾病特异性或扰动特异性网络上进行训练，可以增强模型的预测能力。

另一方面，**STCAT** (Single T Cell Annotation Tool) 和 **TCellAtlas** 数据库提供了**迄今为止最大、最全面的T细胞亚型和状态参考**。它收集了来自35种条件和16种组织的1,348,268个T细胞数据，并将其精细地分为68个高置信度的T细胞亚型/状态。STCAT工具在T细胞注释方面的准确率比现有工具平均高出约28%，并能识别出肺癌中CD4+ Th17细胞的富集、COVID-19中MAIT细胞丰度的下降、以及免疫治疗后CD8+ Tn IFN-response细胞的富集等重要临床发现。它还系统地描绘了Treg细胞不同状态的分子特征，包括转录因子调控、代谢途径和细胞因子响应。TCellAtlas数据库是一个独特的资源，允许用户浏览T细胞表达谱并在线分析定制的scRNA-seq数据。

**现有不足与创新点：**
STCAT和TCellAtlas虽然在T细胞精细分类和疾病情境分析上表现卓越，但它们主要侧重于T细胞的注释、分类和景观分析，并未直接涉足蛋白质功能的情境化表示学习以及基于这些表示的精准治疗靶点发现，而这正是PINNACLE的专长。

本课题的核心创新点在于：**弥合PINNACLE在免疫细胞细致情境（尤其是疾病T细胞状态）方面的局限性，并拓展STCAT/TCellAtlas在精准靶点发现方面的应用。** 我们将**利用STCAT构建的T细胞高精度、疾病情境化参考图谱作为PINNACLE模型训练的核心输入数据**，从而创建一个专为T细胞设计的**“T-cell-PINNACLE”模型**。这个模型将能够生成**前所未有的、T细胞亚型和状态特异性的蛋白质表示**，进而实现更精准的免疫治疗靶点发现和作用机制解析。

**2. 课题名称：整合T细胞高精度图谱与情境感知深度学习的免疫治疗精准靶点发现**

**3. 研究目标**

*   **短期目标**：
    *   **构建T细胞情境化蛋白质相互作用网络和层级元图**：利用STCAT/TCellAtlas中已注释的68种T细胞亚型/状态数据，根据PINNACLE的方法论，为PINNACLE模型构建T细胞亚型/状态特异性蛋白质相互作用网络，并构建反映T细胞亚型间及其与组织间关系的精细化元图。
    *   **训练和优化“T-cell-PINNACLE”模型**：基于上述T细胞特异性输入数据，训练并优化PINNACLE的几何深度学习模型，使其能够生成高度细致且功能相关的T细胞亚型/状态特异性蛋白质表示。
    *   **初步验证靶点提名能力**：在自身免疫疾病（如RA、IBD）和肿瘤免疫（如肺癌、卵巢癌）中，验证“T-cell-PINNACLE”模型在T细胞特异性治疗靶点提名上的性能，并与原版PINNACLE及其他情境无关模型进行对比。

*   **长期目标**：
    *   **深入解析T细胞功能机制**：通过分析T细胞亚型/状态特异性蛋白质表示，结合STCAT已识别的转录因子调控、代谢途径和细胞因子响应等分子特征，深入解析不同T细胞功能状态（如耗竭T细胞、调节性T细胞、记忆T细胞等）的分子机制。
    *   **预测T细胞介导的药物副作用和抗药性机制**：探索“T-cell-PINNACLE”模型在预测T细胞介导的药物脱靶效应（off-target effects）和抗药性机制中的潜力，从而指导药物设计。
    *   **构建交互式探索平台**：开发一个用户友好的交互式平台，让研究人员能够利用“T-cell-PINNACLE”模型，定制化地探索特定T细胞亚型/状态的蛋白质功能，并发现潜在的免疫治疗靶点。

**4. 核心研究内容与方法**

**4.1. 数据收集与预处理**

*   **T细胞单细胞转录组数据来源**：
    *   **核心数据**：利用STCAT/TCellAtlas中已整理的**1,677,799个高质量T细胞**数据，这些数据来源于37个10x Genomics项目和18个Smart-seq2项目，涵盖37种疾病条件和16种组织类型。这些数据已经过严格的质量控制，包括基因/计数过滤、线粒体UMI过滤、CD3D/CD3G阳性细胞筛选、非T细胞标志物（如CD79A、EPCAM、PDGFR4）阴性筛选，以及潜在双重细胞（doublets）的移除。
    *   **注释信息**：直接使用STCAT已提供的**68种精细分类的T细胞亚型/状态注释**，这将是本课题的核心“情境”信息。
*   **蛋白质相互作用（PPI）网络**：沿用PINNACLE的策略，使用人类参考PPI网络，该网络是BioGRID、Human Reference Interactome (HuRI) 和 Menche et al. 的物理多验证相互作用的联合，包含15,461个蛋白质和207,641个相互作用。

**4.2. 情境化网络构建**

*   **T细胞亚型/状态特异性蛋白质相互作用网络的构建**：
    *   **激活基因识别**：对于TCellAtlas中每一个已注释的T细胞亚型/状态（例如CD4+ Th17细胞、CD8+ Tex细胞、Treg细胞毒性状态等），根据PINNACLE的方法，识别在该特定T细胞情境下显著表达的“激活基因”列表。这意味着将TCellAtlas的精细注释（68种T细胞亚型/状态）替换PINNACLE原先使用的粗粒度细胞类型（156种广义细胞类型）。具体方法是：计算每个基因在特定T细胞亚型中的平均表达量与其在数据集中其余细胞中平均表达量的Wilcoxon秩和检验P值，并筛选出Top K个激活基因。
    *   **蛋白质网络提取**：将这些激活基因映射到上述人类参考PPI网络，提取出每个T细胞亚型/状态特异性的蛋白质相互作用子网络。这将生成**68个高度专业化的T细胞情境化PPI网络**，平均每个网络包含约2,530个蛋白质。
*   **T细胞情境的层级元图（Metagraph）构建**：
    *   **精细化元图**：基于STCAT的T细胞分类树和TCellAtlas中已整理的T细胞间通信（已集成CellPhoneDB），构建一个反映T细胞亚型间（如CD4+与CD8+，或CD4+ Th17与CD4+ Treg之间）以及不同状态间（如初始T细胞向效应T细胞分化）相互作用和层级关系的**T细胞特异性元图**。这将比PINNACLE原先的元图在T细胞层面更加细致和专业。
    *   **整合病理情境**：将STCAT已识别的**疾病情境信息（如癌症晚期、COVID-19严重程度、免疫治疗响应状态）**整合到元图或作为额外的情境特征，指导蛋白质表示的学习。这可以通过在元图中添加疾病节点，并与相关T细胞亚型/状态建立联系来实现，或者将疾病状态作为T细胞节点的一个属性。

**4.3. 模型设计与训练**

*   **模型架构**：
    *   采用PINNACLE的**几何深度学习架构**，该架构能够通过在蛋白质、细胞类型和组织之间进行生物学信息传递，学习细胞类型特异性蛋白质表示、细胞类型表示和组织表示，并将它们统一在一个多尺度嵌入空间中。
    *   模型将接收新构建的**68个T细胞亚型/状态特异性PPI网络**和**精细化T细胞情境元图**作为输入。
*   **注意力机制**：沿用PINNACLE的**蛋白质层面带细胞类型特异性的注意力机制**和**元图层面基于细胞相互作用和组织层级的注意力机制**。这些机制确保了蛋白质表示能够精确地根据其所属的T细胞亚型和状态进行调整，并反映T细胞的组织和层级关系。一个“桥接机制”会将元图的结构强加到蛋白质表示中。
*   **预训练目标**：
    *   **蛋白质层面损失（L_protein）**：包括每个T细胞亚型/状态特异性PPI网络中的自监督链路预测（L_ppi）和蛋白质节点T细胞亚型/状态分类（L_celltypeid）。这将使模型能够捕捉每个T细胞情境下PPI网络的拓扑结构，以及不同T细胞中激活蛋白质的细微差异。
    *   **T细胞层面损失（L_celltype）**：包括T细胞亚型-T细胞亚型相互作用预测（L_CCcelltype）和T细胞亚型-组织关系预测（L_CTcelltype）。
    *   **组织层面损失（L_tissue）**：包括组织-组织关系预测（L_TTtissue）和组织-T细胞亚型关系预测（L_TCtissue）。
    *   **整体损失函数**：所有损失分量将通过一个统一的损失函数进行优化。
*   **训练方案**：
    *   **数据划分**：蛋白质-蛋白质边缘将被随机划分为训练（80%）、验证（10%）和测试（10%）集。元图的边缘将不进行划分，因为它们对于模型注入细胞类型和组织信息至关重要。
    *   **负样本采样**：通过结构化负采样（structured_negative_sampling），以1:1的比例随机生成假（负）边缘用于链路预测任务。
    *   **超参数调优**：利用Weights and Biases平台进行随机搜索，优化ROC和Calinski-Harabasz分数。可采用PINNACLE论文中报告的最佳超参数作为初始设置。
    *   **计算资源**：使用高性能计算集群和NVIDIA Tesla V100或同等级GPU进行训练。

**4.4. 下游分析与应用**

*   **T细胞特异性治疗靶点优先级排序**：
    *   **疾病情境选择**：专注于T细胞发挥关键作用的疾病，如肿瘤免疫（肺癌、卵巢癌免疫治疗响应/非响应） 和自身免疫疾病（RA、IBD）。
    *   **模型微调**：将“T-cell-PINNACLE”生成的T细胞亚型/状态特异性蛋白质表示输入到一个多层感知机（MLP）分类器中，进行二分类任务：预测某个蛋白质是否是特定T细胞介导的治疗区域的强候选靶点。
    *   **正负样本构建**：
        *   **正样本**：从Open Targets平台 获取已进入临床试验II期或更高阶段的药物所靶向的蛋白质。这些蛋白质被认为是安全且在初步人体队列中可能有效的。
        *   **负样本**：从DrugBank 获取可成药（druggable）但与目标治疗领域无已知关联的蛋白质。
    *   **关键优势**：与传统情境无关模型相比，本模型将能够预测在**特定T细胞亚型/状态中**（例如，耗竭CD8+ T细胞、致病性Treg细胞）发挥作用的精准靶点，从而提高治疗的精准性和降低副作用。
*   **深入解析T细胞功能分子机制**：
    *   **蛋白质表示差异分析**：通过比较不同T细胞亚型/状态情境下同一蛋白质的表示差异，揭示蛋白质在T细胞激活、分化、耗竭、记忆形成或免疫抑制过程中的精确分子作用机制。
    *   **多维度特征整合**：将“T-cell-PINNACLE”的蛋白质表示与STCAT已发现的Treg细胞的独特分子特征相结合，如转录因子调控子（pySCENIC）、代谢途径富集（scGSVA） 和细胞因子信号响应（CytoSig）。这将提供蛋白质功能情境化的多维度视图。
*   **预测药物脱靶效应与耐药机制**：
    *   利用高度情境化的蛋白质表示，预测药物在非目标T细胞亚型上的潜在脱靶效应，例如，CAR-T细胞疗法中CD19靶向的脱靶神经毒性效应。
    *   识别与免疫治疗耐药相关的特定T细胞亚型/状态中的蛋白质变化，例如STCAT已发现的CD8+ Tn IFN-response细胞在免疫治疗非响应样本中富集，进一步挖掘其中关键蛋白质靶点。
*   **模型的验证与基准测试**：
    *   **内部验证**：评估“T-cell-PINNACLE”模型生成的蛋白质表示是否能准确反映T细胞亚型和组织情境。
    *   **外部验证**：在未用于模型训练的独立疾病队列（如RA、IBD、肿瘤免疫治疗）中，与现有最佳模型（包括原版PINNACLE、CellTypist, SingleR, ProjecTILs 等）在T细胞亚型/状态特异性靶点预测任务上的性能进行严格基准测试，使用APR@K等指标评估性能。

**5. 预期成果与意义**

*   **开创性T细胞蛋白质组学图谱**：构建第一个集成了T细胞高精度亚型/状态信息的蛋白质功能图谱，为免疫学和疾病研究提供前所未有的深度和分辨率。这将是一个比PINNACLE基于Tabula Sapiens数据集更精细、更具特异性的T细胞蛋白质功能图谱。
*   **精准免疫治疗靶点的新范式**：能够识别并预测在特定T细胞亚型/状态中发挥作用的治疗靶点，从而设计更精准、副作用更小的免疫治疗策略，推动个性化医疗的发展。这将直接解决现有药物副作用大、有效性不足的问题。
*   **深层次生物学机制理解**：揭示T细胞在不同生理和病理情境下，蛋白质层面如何执行其复杂功能，为T细胞生物学的基础研究提供新的视角和假设。这将深化对T细胞异质性及其在疾病中作用的理解。
*   **通用计算框架**：所开发的“T-cell-PINNACLE”模型和方法将可推广应用于其他高度异质性的免疫细胞类型（如巨噬细胞、B细胞），为其他领域的精准靶点发现和作用机制解析提供通用框架。
*   **促进转化研究**：通过对临床数据的整合分析，直接将计算预测转化为潜在的诊断生物标志物或预后指标，加速从“实验室到临床”的转化进程。
*   **建立交互式资源**：结合TCellAtlas现有功能，建立一个在线平台，供全球研究人员探索T细胞蛋白质功能和靶点，促进数据共享和协作研究。

**6. 潜在挑战与应对策略**

*   **数据规模与计算资源**：STCAT提供了庞大的T细胞数据（1.6M+细胞），这在PINNACLE模型训练中可能带来巨大的计算挑战。
    *   **应对策略**：
        *   **优化算法**：尽可能优化PINNACLE代码以提高效率，并利用分布式计算框架。
        *   **高效采样**：在构建T细胞亚型/状态特异性PPI网络时，虽然PINNACLE原始方法不进行细胞亚采样，但在生成细胞间相互作用（如CellPhoneDB）时，可以利用STCAT已采用的几何草图绘制（geometric sketching）等高效采样方法，在保留生物复杂性的同时处理大规模数据。
        *   **硬件支持**：利用高性能计算集群和多个GPU进行并行训练。
*   **多尺度数据整合复杂性**：整合单细胞转录组、蛋白质相互作用、T细胞亚型层级和疾病情境等多尺度异构数据本身就具有挑战性。
    *   **应对策略**：严格遵循PINNACLE的多尺度图神经网络设计原则，并利用STCAT在T细胞数据整理和注释方面的专业知识，确保数据质量和一致性。
*   **模型可解释性**：深度学习模型在生物学领域的应用常面临“黑箱”问题，难以直接解释其预测依据。
    *   **应对策略**：结合PINNACLE中使用的注意力机制 和STCAT的分子特征分析（如转录因子调控子、代谢途径、细胞因子响应），共同解释关键蛋白质和通路在特定T细胞情境下的重要性。同时，引入模型不确定性量化方法（如PINNACLE提及的共形预测），帮助评估预测的可靠性。
*   **实验验证难度**：计算预测需要实验验证，尤其是在特定T细胞亚型/状态水平上的蛋白质功能和药物效果验证。
    *   **应对策略**：与实验免疫学家、肿瘤生物学家和药理学家紧密合作，设计小规模、高通量的体外和体内实验，验证模型预测的关键T细胞特异性靶点。例如，利用原代T细胞分离和共培养模型，或基因编辑技术在特定T细胞亚型中进行验证。
