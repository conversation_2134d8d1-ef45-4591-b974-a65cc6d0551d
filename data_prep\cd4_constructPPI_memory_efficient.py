#!/usr/bin/env python3
"""
Memory-Efficient CD4-PINNACLE Data Preprocessing Pipeline
Optimized version for handling large CD4 T cell datasets with memory constraints

This script implements chunking and streaming strategies to process the 411K CD4 cells
without exceeding memory limits.
"""

import os
import sys
import random
import argparse
import pandas as pd
import numpy as np
from collections import Counter
import scanpy as sc
import networkx as nx
import gc
import warnings
warnings.filterwarnings('ignore')

# Add parent directory to path
sys.path.insert(0, '..')
sys.path.insert(0, '.')

from utils import load_global_PPI
from data_config import TABULA_SAPIENS_DIR, PPI_DIR, OUTPUT_DIR

# Configure scanpy for memory efficiency
sc.settings.verbosity = 1
sc.settings.set_figure_params(dpi=80, facecolor='white')
sc.settings.max_memory = 64  # GB limit

def load_cd4_data_chunked(annotation_col='annotation_correction_low', min_cells=100, chunk_size=50000):
    """
    Load CD4 data in chunks to avoid memory issues
    """
    print(f"Loading CD4 data from: {TABULA_SAPIENS_DIR}")
    
    # First pass: get basic info without loading everything
    adata_backed = sc.read_h5ad(TABULA_SAPIENS_DIR, backed='r')
    print(f"Dataset shape: {adata_backed.shape}")
    
    # Sample a subset to get subtype information
    n_sample = min(10000, adata_backed.n_obs)
    sample_indices = np.random.choice(adata_backed.n_obs, n_sample, replace=False)
    sample_obs = adata_backed.obs.iloc[sample_indices]
    
    if annotation_col not in sample_obs.columns:
        raise ValueError(f"Annotation column '{annotation_col}' not found")
    
    sample_counts = sample_obs[annotation_col].value_counts()
    print(f"Sample subtype distribution (from {n_sample} cells):")
    print(sample_counts)
    
    # Close backed file
    adata_backed.file.close()
    
    # Load full dataset with memory optimization
    adata = sc.read_h5ad(TABULA_SAPIENS_DIR)
    
    # Filter subtypes with sufficient cells
    subtype_counts = adata.obs[annotation_col].value_counts()
    valid_subtypes = subtype_counts[subtype_counts >= min_cells].index.tolist()
    
    print(f"\\nValid subtypes (>= {min_cells} cells): {len(valid_subtypes)}")
    
    # Filter to valid subtypes only
    valid_mask = adata.obs[annotation_col].isin(valid_subtypes)
    adata_filtered = adata[valid_mask].copy()
    
    print(f"Filtered data shape: {adata_filtered.shape}")
    
    # Clear original data
    del adata
    gc.collect()
    
    return adata_filtered, valid_subtypes

def rank_genes_memory_efficient(adata, annotation_col, method='wilcoxon', 
                               output_prefix=None, max_subtypes_per_batch=10):
    """
    Memory-efficient gene ranking using batch processing
    """
    print(f"\\nRanking genes using {method} method...")
    print(f"Processing {adata.obs[annotation_col].nunique()} subtypes in batches")
    
    # Get all subtypes
    all_subtypes = adata.obs[annotation_col].unique()
    n_batches = (len(all_subtypes) + max_subtypes_per_batch - 1) // max_subtypes_per_batch
    
    all_results = {}
    
    for batch_idx in range(n_batches):
        start_idx = batch_idx * max_subtypes_per_batch
        end_idx = min((batch_idx + 1) * max_subtypes_per_batch, len(all_subtypes))
        batch_subtypes = all_subtypes[start_idx:end_idx]
        
        print(f"\\nProcessing batch {batch_idx + 1}/{n_batches}: {len(batch_subtypes)} subtypes")
        
        # Create subset for this batch
        batch_mask = adata.obs[annotation_col].isin(batch_subtypes)
        adata_batch = adata[batch_mask].copy()
        
        # Perform ranking for this batch
        try:
            sc.tl.rank_genes_groups(
                adata_batch, 
                annotation_col, 
                method=method,
                n_genes=adata_batch.n_vars,
                use_raw=False
            )
            
            # Extract results
            ranked = adata_batch.uns["rank_genes_groups"]
            groups = ranked["names"].dtype.names
            
            batch_results = {}
            for group in groups:
                if group in batch_subtypes:  # Only process subtypes in current batch
                    batch_results[f"{group}_names"] = ranked["names"][group]
                    batch_results[f"{group}_pvals"] = ranked["pvals"][group]
            
            all_results.update(batch_results)
            
            print(f"✓ Batch {batch_idx + 1} completed: {len(batch_subtypes)} subtypes")
            
        except Exception as e:
            print(f"✗ Error in batch {batch_idx + 1}: {e}")
            continue
        
        # Clean up
        del adata_batch
        gc.collect()
    
    # Combine all results into DataFrame
    print("\\nCombining results...")
    rank_df = pd.DataFrame(all_results)
    
    # Save results
    if output_prefix:
        rank_df.to_csv(f"{output_prefix}.csv", sep="\\t", index=False)
        print(f"✓ Saved ranking results: {output_prefix}.csv")
    
    return rank_df

def extract_cd4_ppi_memory_efficient(rank_file, output_file, ppi_network,
                                   max_pval=1.0, max_genes=4000, lcc=True):
    """
    Memory-efficient PPI network extraction
    """
    print(f"\\nExtracting CD4 subtype-specific PPI networks...")
    
    # Load ranked genes
    print(f"Loading ranked genes from: {rank_file}")
    rank_df = pd.read_csv(rank_file, sep="\\t")
    
    # Extract subtypes
    name_cols = [col for col in rank_df.columns if col.endswith('_names')]
    subtypes = [col.replace('_names', '') for col in name_cols]
    
    print(f"Found {len(subtypes)} CD4 subtypes")
    
    celltype_ppi = {}
    
    if output_file:
        outfile = open(f"{output_file}_pval{max_pval}_genes{max_genes}.csv", "w")
        outfile.write("subtype_index,subtype_name,proteins\\n")
    
    for i, subtype in enumerate(subtypes):
        print(f"\\nProcessing {subtype} ({i+1}/{len(subtypes)})...")
        
        name_col = f"{subtype}_names"
        pval_col = f"{subtype}_pvals"
        
        if name_col not in rank_df.columns or pval_col not in rank_df.columns:
            print(f"  Warning: Missing columns for {subtype}")
            continue
        
        # Filter genes by p-value
        subtype_data = rank_df[[name_col, pval_col]].dropna()
        
        # Convert p-values to numeric, handling any string issues
        try:
            pvals_numeric = pd.to_numeric(subtype_data[pval_col], errors='coerce')
            significant_mask = pvals_numeric <= max_pval
            significant_genes = subtype_data[significant_mask]
        except:
            print(f"  Warning: Could not filter by p-value for {subtype}")
            significant_genes = subtype_data
        
        if len(significant_genes) == 0:
            print(f"  Warning: No significant genes for {subtype}")
            continue
        
        # Select top genes
        top_genes = significant_genes.head(max_genes)[name_col].tolist()
        print(f"  Selected {len(top_genes)} genes")
        
        # Map to PPI network
        ppi_proteins = set(ppi_network.nodes())
        mapped_proteins = [gene for gene in top_genes if gene in ppi_proteins]
        
        print(f"  Mapped to PPI: {len(mapped_proteins)}/{len(top_genes)} genes")
        
        if len(mapped_proteins) == 0:
            print(f"  Warning: No proteins mapped for {subtype}")
            continue
        
        # Extract subnetwork
        try:
            subnetwork = ppi_network.subgraph(mapped_proteins).copy()
            
            # Use largest connected component if requested
            if lcc and len(subnetwork.nodes()) > 0:
                components = list(nx.connected_components(subnetwork))
                if components:
                    largest_cc = max(components, key=len)
                    subnetwork = subnetwork.subgraph(largest_cc).copy()
                    print(f"  LCC: {len(largest_cc)} proteins, {subnetwork.number_of_edges()} edges")
            
            # Store result
            celltype_ppi[subtype] = {
                'proteins': list(subnetwork.nodes()),
                'network': subnetwork,
                'n_proteins': len(subnetwork.nodes()),
                'n_edges': subnetwork.number_of_edges()
            }
            
            # Write to output
            if output_file:
                protein_list = ",".join(subnetwork.nodes())
                outfile.write(f"{i},{subtype},{protein_list}\\n")
                
        except Exception as e:
            print(f"  Error processing subnetwork for {subtype}: {e}")
            continue
    
    if output_file:
        outfile.close()
        print(f"\\n✓ Saved PPI networks to: {output_file}_pval{max_pval}_genes{max_genes}.csv")
    
    # Print summary
    if celltype_ppi:
        print(f"\\n=== Summary ===")
        print(f"Processed subtypes: {len(celltype_ppi)}")
        
        protein_counts = [data['n_proteins'] for data in celltype_ppi.values()]
        edge_counts = [data['n_edges'] for data in celltype_ppi.values()]
        
        print(f"Avg proteins per network: {np.mean(protein_counts):.1f} ± {np.std(protein_counts):.1f}")
        print(f"Avg edges per network: {np.mean(edge_counts):.1f} ± {np.std(edge_counts):.1f}")
        
        # Top networks
        sorted_subtypes = sorted(celltype_ppi.items(), key=lambda x: x[1]['n_proteins'], reverse=True)
        print(f"\\nTop 5 largest networks:")
        for subtype, data in sorted_subtypes[:5]:
            print(f"  {subtype}: {data['n_proteins']} proteins, {data['n_edges']} edges")
    
    return celltype_ppi

def main():
    """Main function with memory management"""
    parser = argparse.ArgumentParser(description="Memory-Efficient CD4-PINNACLE Preprocessing")
    
    parser.add_argument("-rank", type=str, default="True", help="Rank genes (True) or extract PPI (False)")
    parser.add_argument("-rank_pval_filename", type=str, required=True, help="Path for ranked genes")
    parser.add_argument("-celltype_ppi_filename", type=str, default="", help="Path for PPI networks")
    parser.add_argument("-annotation", type=str, default="annotation_correction_low", help="Annotation column")
    parser.add_argument("-min_cells", type=int, default=100, help="Minimum cells per subtype")
    parser.add_argument("-method", type=str, default="wilcoxon", help="Statistical method")
    parser.add_argument("-max_pval", type=float, default=1.0, help="Maximum p-value")
    parser.add_argument("-max_num_genes", type=int, default=4000, help="Maximum genes per subtype")
    parser.add_argument("-lcc", type=str, default="True", help="Use largest connected component")
    parser.add_argument("-chunk_size", type=int, default=50000, help="Chunk size for processing")
    parser.add_argument("-max_subtypes_per_batch", type=int, default=8, help="Max subtypes per batch")
    
    args = parser.parse_args()
    
    # Convert string boolean arguments
    rank = args.rank.lower() == 'true'
    lcc = args.lcc.lower() == 'true'
    
    print("="*60)
    print("Memory-Efficient CD4-PINNACLE Preprocessing")
    print("="*60)
    print(f"Rank mode: {rank}")
    print(f"Chunk size: {args.chunk_size}")
    print(f"Max subtypes per batch: {args.max_subtypes_per_batch}")
    
    # Create output directory
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    if rank:
        print("\\n>>> GENE RANKING PHASE <<<")
        
        # Load data with chunking
        adata, valid_subtypes = load_cd4_data_chunked(
            annotation_col=args.annotation,
            min_cells=args.min_cells,
            chunk_size=args.chunk_size
        )
        
        # Rank genes with batching
        rank_df = rank_genes_memory_efficient(
            adata,
            annotation_col=args.annotation,
            method=args.method,
            output_prefix=args.rank_pval_filename,
            max_subtypes_per_batch=args.max_subtypes_per_batch
        )
        
        print(f"\\n✓ Gene ranking completed!")
        
    else:
        print("\\n>>> PPI EXTRACTION PHASE <<<")
        
        # Load PPI network
        print("Loading global PPI network...")
        ppi_network = load_global_PPI(PPI_DIR)
        print(f"Global PPI: {ppi_network.number_of_nodes()} proteins, {ppi_network.number_of_edges()} edges")
        
        # Extract PPI networks
        celltype_ppi = extract_cd4_ppi_memory_efficient(
            rank_file=args.rank_pval_filename,
            output_file=args.celltype_ppi_filename,
            ppi_network=ppi_network,
            max_pval=args.max_pval,
            max_genes=args.max_num_genes,
            lcc=lcc
        )
        
        print(f"\\n✓ PPI extraction completed!")
    
    print(f"\\n{'='*60}")
    print("Memory-efficient preprocessing completed!")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()