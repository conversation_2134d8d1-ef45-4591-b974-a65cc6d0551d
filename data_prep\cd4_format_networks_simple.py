#!/usr/bin/env python3
"""
Simple parser for CD4 PPI networks to PINNACLE format
"""

import networkx as nx
import os
import sys

# Add parent directory to path
sys.path.insert(0, '..')

def load_global_ppi():
    """Load global PPI network"""
    ppi_file = "../data/networks/global_ppi_edgelist.txt"
    print(f"Loading global PPI from: {ppi_file}")
    
    G = nx.Graph()
    with open(ppi_file, 'r') as f:
        for line in f:
            if line.strip():
                parts = line.strip().split()  # Split by any whitespace
                if len(parts) >= 2:
                    G.add_edge(parts[0], parts[1])
    
    print(f"Global PPI: {G.number_of_nodes()} nodes, {G.number_of_edges()} edges")
    return G

def parse_cd4_networks():
    """Parse CD4 networks from the raw file"""
    cd4_file = "../data/networks/cd4_celltype_ppi_pval1.0_genes4000.csv"
    print(f"Parsing CD4 networks from: {cd4_file}")
    
    with open(cd4_file, 'r') as f:
        content = f.read()
    
    # Replace escaped newlines with actual newlines
    content = content.replace('\\n', '\n')
    
    lines = content.strip().split('\n')
    print(f"Total lines: {len(lines)}")
    
    # Find header line
    header_line = None
    for i, line in enumerate(lines):
        if 'subtype_index,subtype_name,proteins' in line:
            header_line = i
            break
    
    if header_line is None:
        print("Header not found, trying first line...")
        header_line = 0
    
    print(f"Header found at line {header_line}")
    
    networks = []
    i = header_line + 1
    
    while i < len(lines):
        line = lines[i].strip()
        if not line:
            i += 1
            continue
        
        parts = line.split(',', 2)  # Split into at most 3 parts
        if len(parts) >= 3:
            try:
                subtype_index = int(parts[0])
                subtype_name = parts[1]
                proteins_str = parts[2]
                
                # Split proteins and clean them
                proteins = [p.strip() for p in proteins_str.split(',') if p.strip()]
                
                networks.append({
                    'index': subtype_index,
                    'name': subtype_name,
                    'proteins': proteins
                })
                
                print(f"Parsed: {subtype_name} with {len(proteins)} proteins")
                
            except ValueError as e:
                print(f"Error parsing line {i}: {e}")
        
        i += 1
    
    print(f"Successfully parsed {len(networks)} CD4 subtypes")
    return networks

def convert_to_edgelists(networks, global_ppi):
    """Convert networks to edgelist format"""
    output_dir = "../data/networks/ppi_edgelists"
    os.makedirs(output_dir, exist_ok=True)
    
    networks_created = 0
    total_edges = 0
    
    for network in networks:
        subtype_name = network['name']
        proteins = network['proteins']
        
        print(f"\nProcessing {subtype_name}: {len(proteins)} proteins")
        
        # Extract subnetwork from global PPI
        subnetwork = global_ppi.subgraph(proteins).copy()
        
        if subnetwork.number_of_edges() == 0:
            print(f"  Warning: No edges found for {subtype_name}")
            continue
        
        # Create safe filename
        safe_name = subtype_name.replace(' ', '_').replace('/', '_').replace('-', '_')
        safe_name = ''.join(c for c in safe_name if c.isalnum() or c in '_')
        
        output_file = os.path.join(output_dir, f"{safe_name}.txt")
        
        with open(output_file, 'w') as f:
            for edge in subnetwork.edges():
                f.write(f"{edge[0]}\t{edge[1]}\n")
        
        networks_created += 1
        total_edges += subnetwork.number_of_edges()
        
        print(f"  Saved: {output_file}")
        print(f"  Edges: {subnetwork.number_of_edges()}")
    
    print(f"\n=== Summary ===")
    print(f"Networks created: {networks_created}")
    print(f"Total edges: {total_edges}")
    if networks_created > 0:
        print(f"Average edges per network: {total_edges/networks_created:.1f}")
    
    return networks_created

def create_subtype_files(networks):
    """Create subtype list and metagraph files"""
    
    # Create subtypes list
    with open("../data/networks/cd4_valid_subtypes.csv", 'w') as f:
        f.write("index,subtype,safe_name,protein_count\n")
        for network in networks:
            safe_name = network['name'].replace(' ', '_').replace('/', '_').replace('-', '_')
            safe_name = ''.join(c for c in safe_name if c.isalnum() or c in '_')
            f.write(f"{network['index']},{network['name']},{safe_name},{len(network['proteins'])}\n")
    
    print(f"Created subtypes list: ../data/networks/cd4_valid_subtypes.csv")
    
    # Create simple metagraph
    safe_names = []
    for network in networks:
        safe_name = network['name'].replace(' ', '_').replace('/', '_').replace('-', '_')
        safe_name = ''.join(c for c in safe_name if c.isalnum() or c in '_')
        safe_names.append(safe_name)
    
    with open("../data/networks/cd4_mg_edgelist.txt", 'w') as f:
        # Create chain connections
        for i in range(len(safe_names)-1):
            f.write(f"{safe_names[i]}\t{safe_names[i+1]}\n")
        
        # Add some functional group connections
        treg = [s for s in safe_names if 'Treg' in s]
        naive = [s for s in safe_names if 'Naive' in s]
        memory = [s for s in safe_names if 'Memory' in s or 'Mem' in s]
        
        for group in [treg, naive, memory]:
            for i in range(len(group)-1):
                f.write(f"{group[i]}\t{group[i+1]}\n")
    
    print(f"Created metagraph: ../data/networks/cd4_mg_edgelist.txt")

def main():
    """Main conversion function"""
    print("="*60)
    print("Converting CD4 Networks to PINNACLE Format (Simple Parser)")
    print("="*60)
    
    # Parse CD4 networks
    networks = parse_cd4_networks()
    
    if not networks:
        print("No networks found!")
        return
    
    # Load global PPI
    global_ppi = load_global_ppi()
    
    # Convert to edgelists
    networks_created = convert_to_edgelists(networks, global_ppi)
    
    # Create additional files
    create_subtype_files(networks)
    
    print(f"\n✓ Conversion completed!")
    print(f"✓ {networks_created} CD4 subtype networks ready for training")

if __name__ == "__main__":
    main()