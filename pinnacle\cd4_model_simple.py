#!/usr/bin/env python3
"""
Simplified CD4-PINNACLE Model
A streamlined version of PINNACLE optimized for CD4 T cell subtype analysis
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_geometric.nn import GCNConv, GATConv, global_mean_pool, BatchNorm

class CD4PinnacleSimple(nn.Module):
    """Simplified CD4-PINNACLE model using standard GNN layers"""
    
    def __init__(self, input_dim, hidden_dim, output_dim, num_subtypes, n_heads=4, dropout=0.3):
        super(CD4PinnacleSimple, self).__init__()
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.output_dim = output_dim
        self.num_subtypes = num_subtypes
        self.n_heads = n_heads
        self.dropout = dropout
        
        # Protein-level GNN layers
        self.protein_conv1 = GATConv(input_dim, hidden_dim, heads=n_heads, dropout=dropout)
        self.protein_conv2 = GATConv(hidden_dim * n_heads, output_dim, heads=1, dropout=dropout)
        
        # Normalization layers
        self.norm1 = BatchNorm(hidden_dim * n_heads)
        self.norm2 = BatchNorm(output_dim)
        
        # Subtype-specific parameters
        self.subtype_embeddings = nn.Parameter(torch.randn(num_subtypes, output_dim))
        self.subtype_attention = nn.MultiheadAttention(output_dim, num_heads=4, dropout=dropout)
        
        # Link prediction decoder
        self.link_predictor = nn.Sequential(
            nn.Linear(output_dim * 2, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, 1)
        )
        
        # Subtype classifier
        self.subtype_classifier = nn.Linear(output_dim, num_subtypes)
        
        self.reset_parameters()
    
    def reset_parameters(self):
        """Initialize parameters"""
        nn.init.xavier_uniform_(self.subtype_embeddings)
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
    
    def forward(self, x, edge_index, batch=None, subtype_id=0):
        """Forward pass for a single subtype network"""
        
        # Protein-level message passing
        h1 = self.protein_conv1(x, edge_index)
        h1 = self.norm1(h1)
        h1 = F.relu(h1)
        h1 = F.dropout(h1, p=self.dropout, training=self.training)
        
        h2 = self.protein_conv2(h1, edge_index)
        h2 = self.norm2(h2)
        h2 = F.relu(h2)
        
        # Incorporate subtype context
        subtype_emb = self.subtype_embeddings[subtype_id].unsqueeze(0).expand(h2.size(0), -1)
        
        # Attention mechanism for subtype-specific features
        h2_attended, _ = self.subtype_attention(
            h2.unsqueeze(1),  # Query
            subtype_emb.unsqueeze(1),  # Key
            subtype_emb.unsqueeze(1)   # Value
        )
        h2_attended = h2_attended.squeeze(1)
        
        # Combine original and attended features
        protein_embeddings = h2 + h2_attended
        
        return protein_embeddings
    
    def predict_links(self, embeddings, edge_index):
        """Predict links between proteins"""
        row, col = edge_index
        edge_embeddings = torch.cat([embeddings[row], embeddings[col]], dim=1)
        link_scores = self.link_predictor(edge_embeddings).squeeze(-1)
        return link_scores
    
    def get_protein_embeddings(self, x, edge_index, subtype_id=0):
        """Get protein embeddings for a specific subtype"""
        return self.forward(x, edge_index, subtype_id=subtype_id)
    
    def classify_subtype(self, protein_embeddings):
        """Classify proteins into subtypes"""
        # Global pooling
        graph_embedding = torch.mean(protein_embeddings, dim=0, keepdim=True)
        subtype_logits = self.subtype_classifier(graph_embedding)
        return subtype_logits

class CD4PinnacleTrainer:
    """Simplified trainer for CD4-PINNACLE"""
    
    def __init__(self, model, device='cpu'):
        self.model = model.to(device)
        self.device = device
        
        # Loss functions
        self.link_criterion = nn.BCEWithLogitsLoss()
        self.subtype_criterion = nn.CrossEntropyLoss()
        
        # Optimizers
        self.optimizer = torch.optim.Adam(model.parameters(), lr=0.001, weight_decay=5e-4)
        self.scheduler = torch.optim.lr_scheduler.StepLR(self.optimizer, step_size=50, gamma=0.5)
    
    def negative_sampling(self, edge_index, num_nodes, num_neg_samples=None):
        """Generate negative edges for link prediction"""
        if num_neg_samples is None:
            num_neg_samples = edge_index.size(1)
        
        # Convert to set for fast lookup
        pos_edges = set(map(tuple, edge_index.t().cpu().numpy()))
        
        neg_edges = []
        while len(neg_edges) < num_neg_samples:
            i = torch.randint(0, num_nodes, (1,)).item()
            j = torch.randint(0, num_nodes, (1,)).item()
            if i != j and (i, j) not in pos_edges and (j, i) not in pos_edges:
                neg_edges.append([i, j])
        
        return torch.tensor(neg_edges, device=self.device).t()
    
    def train_step(self, data_dict, epoch):
        """Single training step"""
        self.model.train()
        total_loss = 0
        link_losses = []
        
        for subtype_id, (context, data) in enumerate(data_dict.items()):
            data = data.to(self.device)
            
            # Forward pass
            embeddings = self.model(data.x, data.edge_index, subtype_id=subtype_id)
            
            # Link prediction task
            pos_edge_index = data.edge_index[:, data.train_mask]
            neg_edge_index = self.negative_sampling(pos_edge_index, data.x.size(0))
            
            # Positive and negative scores
            pos_scores = self.model.predict_links(embeddings, pos_edge_index)
            neg_scores = self.model.predict_links(embeddings, neg_edge_index)
            
            # Link prediction loss
            pos_labels = torch.ones(pos_scores.size(0), device=self.device)
            neg_labels = torch.zeros(neg_scores.size(0), device=self.device)
            
            link_loss = self.link_criterion(
                torch.cat([pos_scores, neg_scores]),
                torch.cat([pos_labels, neg_labels])
            )
            
            link_losses.append(link_loss.item())
            total_loss += link_loss
        
        # Backward pass
        self.optimizer.zero_grad()
        total_loss.backward()
        torch.nn.utils.clip_grad_norm_(self.model.parameters(), 1.0)
        self.optimizer.step()
        
        return {
            'total_loss': total_loss.item() / len(data_dict),
            'link_loss': sum(link_losses) / len(link_losses)
        }
    
    def train(self, data_dict, epochs=100, log_every=10):
        """Training loop"""
        print(f"Training CD4-PINNACLE for {epochs} epochs...")
        
        best_loss = float('inf')
        patience = 0
        max_patience = 20
        
        for epoch in range(epochs):
            # Training step
            metrics = self.train_step(data_dict, epoch)
            
            # Learning rate scheduling
            self.scheduler.step()
            
            # Logging
            if epoch % log_every == 0:
                print(f"Epoch {epoch:3d}: Loss={metrics['total_loss']:.4f}, "
                      f"Link={metrics['link_loss']:.4f}, "
                      f"LR={self.scheduler.get_last_lr()[0]:.6f}")
            
            # Early stopping
            if metrics['total_loss'] < best_loss:
                best_loss = metrics['total_loss']
                patience = 0
            else:
                patience += 1
                if patience >= max_patience:
                    print(f"Early stopping at epoch {epoch}")
                    break
        
        print(f"Training completed! Best loss: {best_loss:.4f}")
    
    def generate_embeddings(self, data_dict):
        """Generate embeddings for all subtypes"""
        self.model.eval()
        embeddings_dict = {}
        
        with torch.no_grad():
            for subtype_id, (context, data) in enumerate(data_dict.items()):
                data = data.to(self.device)
                embeddings = self.model(data.x, data.edge_index, subtype_id=subtype_id)
                embeddings_dict[context] = embeddings.cpu()
        
        return embeddings_dict

def main():
    """Test the simplified CD4-PINNACLE model"""
    print("Testing simplified CD4-PINNACLE model...")
    
    # Load data
    from cd4_generate_input import read_cd4_data
    data = read_cd4_data(
        global_ppi_f="../data/networks/global_ppi_edgelist.txt",
        ppi_dir="../data/networks/ppi_edgelists/",
        mg_f="../data/networks/cd4_mg_edgelist.txt"
    )
    
    # Create model
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = CD4PinnacleSimple(
        input_dim=2048,
        hidden_dim=128,
        output_dim=64,
        num_subtypes=len(data['ppi_data']),
        n_heads=4,
        dropout=0.3
    )
    
    # Create trainer
    trainer = CD4PinnacleTrainer(model, device)
    
    # Train model (small test)
    trainer.train(data['ppi_data'], epochs=10, log_every=2)
    
    # Generate embeddings
    embeddings = trainer.generate_embeddings(data['ppi_data'])
    
    print(f"\nGenerated embeddings for {len(embeddings)} CD4 subtypes")
    for context, emb in list(embeddings.items())[:3]:
        print(f"  {context}: {emb.shape}")
    
    print("Simple CD4-PINNACLE test completed!")
    return trainer, embeddings

if __name__ == "__main__":
    trainer, embeddings = main()