#!/usr/bin/env python3
"""
CD4-PINNACLE Data Preprocessing Pipeline
Specialized script for constructing CD4 T cell subtype-specific PPI networks

This script adapts the original PINNACLE preprocessing pipeline for CD4 T cell data,
using the annotation_correction_low column for high-resolution subtype classification.

Usage:
    python cd4_constructPPI.py -rank True -rank_pval_filename ../data/networks/cd4_ranked_genes
    python cd4_constructPPI.py -rank False -rank_pval_filename ../data/networks/cd4_ranked_genes.csv -celltype_ppi_filename ../data/networks/cd4_celltype_ppi -max_pval 1 -max_num_genes 4000
"""

import random
import glob
import argparse
import pandas as pd
import numpy as np
from collections import Counter
import scanpy as sc
import networkx as nx
import os
import sys

# Add parent directory to path for imports
sys.path.insert(0, '..')
sys.path.insert(0, '.')

from utils import load_global_PPI, read_ts_data, count_cells_per_celltype, calculate_correlation
from data_config import TABULA_SAPIENS_DIR, PPI_DIR, OUTPUT_DIR, CD4_RANKED_GENES_DIR, CD4_CELLTYPE_PPI_DIR

# Configure scanpy
sc.settings.verbosity = 1
sc.settings.set_figure_params(dpi=80, facecolor='white')

def load_cd4_data(annotation_col='annotation_correction_low', min_cells=100):
    """
    Load and preprocess CD4 T cell data with quality control
    
    Parameters:
    -----------
    annotation_col : str
        Column name for CD4 subtype annotations
    min_cells : int
        Minimum number of cells required per subtype
        
    Returns:
    --------
    adata : AnnData
        Preprocessed CD4 T cell data
    valid_subtypes : list
        List of subtypes with sufficient cells
    """
    print(f"Loading CD4 data from: {TABULA_SAPIENS_DIR}")
    adata = sc.read_h5ad(TABULA_SAPIENS_DIR)
    
    print(f"Original data shape: {adata.shape}")
    print(f"Using annotation column: {annotation_col}")
    
    if annotation_col not in adata.obs.columns:
        raise ValueError(f"Annotation column '{annotation_col}' not found in data")
    
    # Count cells per subtype
    subtype_counts = adata.obs[annotation_col].value_counts()
    print(f"\\nCD4 subtypes found: {len(subtype_counts)}")
    print(f"Total cells: {adata.n_obs:,}")
    
    # Filter subtypes with sufficient cells
    valid_subtypes = subtype_counts[subtype_counts >= min_cells].index.tolist()
    print(f"\\nSubtypes with >= {min_cells} cells: {len(valid_subtypes)}")
    
    # Filter data to include only valid subtypes
    valid_mask = adata.obs[annotation_col].isin(valid_subtypes)
    adata_filtered = adata[valid_mask].copy()
    
    print(f"Filtered data shape: {adata_filtered.shape}")
    print(f"Cells retained: {adata_filtered.n_obs:,} ({adata_filtered.n_obs/adata.n_obs*100:.1f}%)")
    
    # Print subtype distribution
    print(f"\\nValid CD4 subtypes:")
    filtered_counts = adata_filtered.obs[annotation_col].value_counts()
    for subtype, count in filtered_counts.head(15).items():
        print(f"  {subtype}: {count:,} cells")
    
    if len(filtered_counts) > 15:
        print(f"  ... and {len(filtered_counts) - 15} more subtypes")
    
    return adata_filtered, valid_subtypes

def subsample_cells_cd4(adata, groupby, num_cells_cutoff=None, random_state=42):
    """
    Subsample cells per CD4 subtype for balanced analysis
    
    Parameters:
    -----------
    adata : AnnData
        Input CD4 T cell data
    groupby : str
        Column name for grouping (CD4 subtypes)
    num_cells_cutoff : int
        Maximum number of cells per subtype (None for no subsampling)
    random_state : int
        Random seed for reproducibility
        
    Returns:
    --------
    adata_sampled : AnnData
        Subsampled data
    """
    if num_cells_cutoff is None:
        print("No subsampling applied")
        return adata.copy()
    
    print(f"Subsampling to max {num_cells_cutoff} cells per subtype...")
    random.seed(random_state)
    np.random.seed(random_state)
    
    subtype_counts = Counter(adata.obs[groupby].tolist())
    sampled_data = []
    
    for subtype, count in subtype_counts.items():
        subtype_data = adata[adata.obs[groupby] == subtype]
        
        if count <= num_cells_cutoff:
            # Keep all cells if below threshold
            sample = subtype_data
            print(f"  {subtype}: {count} cells (kept all)")
        else:
            # Randomly sample cells
            sample_idx = np.random.choice(count, num_cells_cutoff, replace=False)
            sample = subtype_data[sample_idx]
            print(f"  {subtype}: {count} -> {num_cells_cutoff} cells")
        
        if len(sampled_data) == 0:
            sampled_data = sample
        else:
            sampled_data = sampled_data.concatenate(sample, join="inner")
    
    print(f"Final sampled data shape: {sampled_data.shape}")
    return sampled_data

def rank_genes_cd4(adata, annotation_col, method='wilcoxon', output_prefix=None):
    """
    Rank genes for each CD4 subtype using statistical tests
    
    Parameters:
    -----------
    adata : AnnData
        CD4 T cell data
    annotation_col : str
        Column for CD4 subtype annotations
    method : str
        Statistical method for ranking ('wilcoxon', 't-test', etc.)
    output_prefix : str
        Prefix for output files
        
    Returns:
    --------
    adata : AnnData
        Data with ranking results
    rank_df : DataFrame
        Ranked genes dataframe
    """
    print(f"Ranking genes using {method} method by {annotation_col}...")
    print(f"Subtypes: {adata.obs[annotation_col].nunique()}")
    
    # Perform differential expression analysis
    sc.tl.rank_genes_groups(adata, annotation_col, method=method, n_genes=adata.n_vars)
    
    # Save results if output prefix provided
    if output_prefix:
        print(f"Saving ranked genes to {output_prefix}...")
        
        # Save AnnData with rankings
        adata.write(f"{output_prefix}.h5ad")
        
        # Extract and save ranking table
        ranked = adata.uns["rank_genes_groups"]
        groups = ranked["names"].dtype.names
        
        rank_df = pd.DataFrame({
            group + "_" + key[:1]: ranked[key][group] 
            for group in groups 
            for key in ["names", "pvals"]
        })
        
        rank_df.to_csv(f"{output_prefix}.csv", sep="\t", index=False)
        print(f"Saved ranking results: {output_prefix}.h5ad and {output_prefix}.csv")
        
        return adata, rank_df
    
    return adata, None

def extract_cd4_celltype_ppi(rank_file, output_file, ppi_network, 
                           max_pval=1.0, max_genes=4000, lcc=True):
    """
    Extract CD4 subtype-specific PPI networks from ranked genes
    
    Parameters:
    -----------
    rank_file : str
        Path to ranked genes CSV file
    output_file : str
        Path for output PPI networks
    ppi_network : networkx.Graph
        Global PPI network
    max_pval : float
        Maximum p-value threshold for gene selection
    max_genes : int
        Maximum number of genes per subtype
    lcc : bool
        Use largest connected component only
        
    Returns:
    --------
    celltype_ppi : dict
        Dictionary of subtype-specific PPI networks
    """
    print(f"Extracting CD4 subtype-specific PPI networks...")
    print(f"Parameters: max_pval={max_pval}, max_genes={max_genes}, lcc={lcc}")
    
    # Load ranked genes
    rank_df = pd.read_csv(rank_file, sep="\\t")
    print(f"Loaded rankings for {len(rank_df.columns)//2} CD4 subtypes")
    
    # Extract CD4 subtypes (remove "_n" and "_p" suffixes)
    subtypes = list(set([col.rsplit("_", 1)[0] for col in rank_df.columns]))
    subtypes = [s for s in subtypes if not s.endswith('Unnamed')]
    print(f"Processing {len(subtypes)} CD4 subtypes")
    
    celltype_ppi = {}
    
    if output_file:
        outfile = open(f"{output_file}_pval{max_pval}_genes{max_genes}.csv", "w")
        outfile.write("subtype_index,subtype_name,proteins\\n")
    
    for i, subtype in enumerate(subtypes):
        print(f"\\nProcessing {subtype} ({i+1}/{len(subtypes)})...")
        
        # Get gene names and p-values for this subtype
        name_col = f"{subtype}_n"
        pval_col = f"{subtype}_p"
        
        if name_col not in rank_df.columns or pval_col not in rank_df.columns:
            print(f"  Warning: Missing columns for {subtype}, skipping")
            continue
        
        # Filter genes by p-value and select top genes
        genes_data = rank_df[[name_col, pval_col]].dropna()
        significant_genes = genes_data[
            pd.to_numeric(genes_data[pval_col], errors='coerce') <= max_pval
        ]
        
        if len(significant_genes) == 0:
            print(f"  Warning: No significant genes for {subtype}")
            continue
        
        # Select top genes
        top_genes = significant_genes.head(max_genes)[name_col].tolist()
        print(f"  Selected {len(top_genes)} genes (from {len(significant_genes)} significant)")
        
        # Map genes to PPI network
        ppi_proteins = set(ppi_network.nodes())
        mapped_proteins = [gene for gene in top_genes if gene in ppi_proteins]
        
        print(f"  Mapped to PPI: {len(mapped_proteins)}/{len(top_genes)} genes")
        
        if len(mapped_proteins) == 0:
            print(f"  Warning: No proteins mapped to PPI for {subtype}")
            continue
        
        # Extract subnetwork
        subnetwork = ppi_network.subgraph(mapped_proteins).copy()
        
        # Use largest connected component if requested
        if lcc and len(subnetwork.nodes()) > 0:
            components = list(nx.connected_components(subnetwork))
            if components:
                largest_cc = max(components, key=len)
                subnetwork = subnetwork.subgraph(largest_cc).copy()
                print(f"  LCC: {len(largest_cc)} proteins, {subnetwork.number_of_edges()} edges")
        
        # Store result
        celltype_ppi[subtype] = {
            'proteins': list(subnetwork.nodes()),
            'network': subnetwork,
            'n_proteins': len(subnetwork.nodes()),
            'n_edges': subnetwork.number_of_edges()
        }
        
        # Write to output file
        if output_file:
            protein_list = ",".join(subnetwork.nodes())
            outfile.write(f"{i},{subtype},{protein_list}\\n")
    
    if output_file:
        outfile.close()
        print(f"\\nSaved CD4 subtype PPI networks to: {output_file}_pval{max_pval}_genes{max_genes}.csv")
    
    # Print summary statistics
    print(f"\\n=== CD4 Subtype PPI Network Summary ===")
    print(f"Subtypes processed: {len(celltype_ppi)}")
    
    if celltype_ppi:
        protein_counts = [data['n_proteins'] for data in celltype_ppi.values()]
        edge_counts = [data['n_edges'] for data in celltype_ppi.values()]
        
        print(f"Proteins per network: {np.mean(protein_counts):.1f} ± {np.std(protein_counts):.1f}")
        print(f"Edges per network: {np.mean(edge_counts):.1f} ± {np.std(edge_counts):.1f}")
        print(f"Range: {min(protein_counts)}-{max(protein_counts)} proteins")
        
        # Show top 10 largest networks
        sorted_subtypes = sorted(celltype_ppi.items(), 
                               key=lambda x: x[1]['n_proteins'], 
                               reverse=True)
        print(f"\\nTop 10 largest networks:")
        for subtype, data in sorted_subtypes[:10]:
            print(f"  {subtype}: {data['n_proteins']} proteins, {data['n_edges']} edges")
    
    return celltype_ppi

def main():
    """Main function for CD4 PPI network construction"""
    parser = argparse.ArgumentParser(description="CD4-PINNACLE PPI network construction")
    
    # Main operation flags
    parser.add_argument("-rank", type=bool, default=True, 
                       help="Rank genes (True) or extract PPI networks (False)")
    
    # Input/output files
    parser.add_argument("-rank_pval_filename", type=str, required=True,
                       help="Path for ranked genes output (rank=True) or input (rank=False)")
    parser.add_argument("-celltype_ppi_filename", type=str, default="",
                       help="Path for CD4 subtype PPI networks output (rank=False only)")
    
    # Data parameters
    parser.add_argument("-annotation", type=str, default="annotation_correction_low",
                       help="Column name for CD4 subtype annotations")
    parser.add_argument("-min_cells", type=int, default=100,
                       help="Minimum cells per subtype")
    parser.add_argument("-subsample", type=bool, default=False,
                       help="Subsample cells per subtype")
    parser.add_argument("-max_cells_per_subtype", type=int, default=5000,
                       help="Maximum cells per subtype when subsampling")
    
    # Gene ranking parameters
    parser.add_argument("-method", type=str, default="wilcoxon",
                       help="Statistical method for gene ranking")
    
    # PPI extraction parameters
    parser.add_argument("-max_pval", type=float, default=1.0,
                       help="Maximum p-value for gene selection")
    parser.add_argument("-max_num_genes", type=int, default=4000,
                       help="Maximum number of genes per subtype")
    parser.add_argument("-lcc", type=bool, default=True,
                       help="Use largest connected component")
    
    args = parser.parse_args()
    
    # Create output directory if it doesn't exist
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    print("="*60)
    print("CD4-PINNACLE Data Preprocessing Pipeline")
    print("="*60)
    
    if args.rank:
        print("\\n>>> PHASE 1: Gene Ranking <<<")
        
        # Load CD4 data
        adata, valid_subtypes = load_cd4_data(
            annotation_col=args.annotation,
            min_cells=args.min_cells
        )
        
        # Optional subsampling
        if args.subsample:
            adata = subsample_cells_cd4(
                adata, 
                groupby=args.annotation,
                num_cells_cutoff=args.max_cells_per_subtype
            )
        
        # Rank genes
        adata, rank_df = rank_genes_cd4(
            adata,
            annotation_col=args.annotation,
            method=args.method,
            output_prefix=args.rank_pval_filename
        )
        
        print(f"\\n✓ Gene ranking completed successfully!")
        print(f"✓ Results saved with prefix: {args.rank_pval_filename}")
        
    else:
        print("\\n>>> PHASE 2: PPI Network Extraction <<<")
        
        # Load global PPI network
        print("Loading global PPI network...")
        ppi_network = load_global_PPI(PPI_DIR)
        print(f"Global PPI: {ppi_network.number_of_nodes()} proteins, {ppi_network.number_of_edges()} edges")
        
        # Extract CD4 subtype-specific networks
        celltype_ppi = extract_cd4_celltype_ppi(
            rank_file=args.rank_pval_filename,
            output_file=args.celltype_ppi_filename,
            ppi_network=ppi_network,
            max_pval=args.max_pval,
            max_genes=args.max_num_genes,
            lcc=args.lcc
        )
        
        print(f"\\n✓ PPI network extraction completed successfully!")
        print(f"✓ {len(celltype_ppi)} CD4 subtype-specific networks created")
        
    print(f"\\n{'='*60}")
    print("CD4-PINNACLE preprocessing completed!")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()