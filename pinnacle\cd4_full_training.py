#!/usr/bin/env python3
"""
Full CD4-PINNACLE Training Pipeline
Complete training with validation, evaluation, and embedding generation
"""

import os
import time
import numpy as np
import pandas as pd
import torch
import torch.nn.functional as F
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import accuracy_score, f1_score, roc_auc_score
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA
import umap

from cd4_generate_input import read_cd4_data
from cd4_model_simple import CD4PinnacleSimple, CD4PinnacleTrainer

class CD4FullTrainer(CD4PinnacleTrainer):
    """Enhanced trainer with validation and evaluation"""
    
    def __init__(self, model, device='cpu', save_dir="../results/cd4_pinnacle"):
        super().__init__(model, device)
        self.save_dir = save_dir
        os.makedirs(save_dir, exist_ok=True)
        
        # Training history
        self.train_losses = []
        self.val_losses = []
        self.metrics_history = []
    
    def validate_step(self, data_dict):
        """Validation step"""
        self.model.eval()
        val_losses = []
        val_accuracies = []
        
        with torch.no_grad():
            for subtype_id, (context, data) in enumerate(data_dict.items()):
                data = data.to(self.device)
                
                # Forward pass
                embeddings = self.model(data.x, data.edge_index, subtype_id=subtype_id)
                
                # Validation on held-out edges
                if hasattr(data, 'val_mask') and data.val_mask.sum() > 0:
                    val_edge_index = data.edge_index[:, data.val_mask]
                    neg_edge_index = self.negative_sampling(val_edge_index, data.x.size(0))
                    
                    # Predict links
                    pos_scores = self.model.predict_links(embeddings, val_edge_index)
                    neg_scores = self.model.predict_links(embeddings, neg_edge_index)
                    
                    # Calculate validation loss
                    pos_labels = torch.ones(pos_scores.size(0), device=self.device)
                    neg_labels = torch.zeros(neg_scores.size(0), device=self.device)
                    
                    val_loss = self.link_criterion(
                        torch.cat([pos_scores, neg_scores]),
                        torch.cat([pos_labels, neg_labels])
                    )
                    val_losses.append(val_loss.item())
                    
                    # Calculate accuracy
                    all_preds = torch.cat([torch.sigmoid(pos_scores), torch.sigmoid(neg_scores)])
                    all_labels = torch.cat([pos_labels, neg_labels])
                    accuracy = accuracy_score(
                        all_labels.cpu().numpy(), 
                        (all_preds > 0.5).cpu().numpy()
                    )
                    val_accuracies.append(accuracy)
        
        return {
            'val_loss': np.mean(val_losses) if val_losses else 0,
            'val_accuracy': np.mean(val_accuracies) if val_accuracies else 0
        }
    
    def train_with_validation(self, data_dict, epochs=100, log_every=10, save_every=25):
        """Training with validation and checkpointing"""
        print(f"Training CD4-PINNACLE for {epochs} epochs with validation...")
        
        best_val_loss = float('inf')
        patience = 0
        max_patience = 30
        
        for epoch in range(epochs):
            start_time = time.time()
            
            # Training step
            train_metrics = self.train_step(data_dict, epoch)
            
            # Validation step
            val_metrics = self.validate_step(data_dict)
            
            # Learning rate scheduling
            self.scheduler.step()
            
            # Store metrics
            self.train_losses.append(train_metrics['total_loss'])
            self.val_losses.append(val_metrics['val_loss'])
            self.metrics_history.append({
                'epoch': epoch,
                'train_loss': train_metrics['total_loss'],
                'val_loss': val_metrics['val_loss'],
                'val_accuracy': val_metrics['val_accuracy'],
                'lr': self.scheduler.get_last_lr()[0],
                'time': time.time() - start_time
            })
            
            # Logging
            if epoch % log_every == 0:
                print(f"Epoch {epoch:3d}: Train={train_metrics['total_loss']:.4f}, "
                      f"Val={val_metrics['val_loss']:.4f}, "
                      f"Acc={val_metrics['val_accuracy']:.3f}, "
                      f"LR={self.scheduler.get_last_lr()[0]:.6f}, "
                      f"Time={time.time() - start_time:.1f}s")
            
            # Save checkpoint
            if epoch % save_every == 0 or epoch == epochs - 1:
                self.save_checkpoint(epoch, train_metrics, val_metrics)
            
            # Early stopping based on validation loss
            if val_metrics['val_loss'] > 0 and val_metrics['val_loss'] < best_val_loss:
                best_val_loss = val_metrics['val_loss']
                patience = 0
                self.save_best_model(epoch, train_metrics, val_metrics)
            else:
                patience += 1
                if patience >= max_patience:
                    print(f"Early stopping at epoch {epoch}")
                    break
        
        print(f"Training completed! Best validation loss: {best_val_loss:.4f}")
        
        # Save training history
        self.save_training_history()
        
        return self.metrics_history
    
    def save_checkpoint(self, epoch, train_metrics, val_metrics):
        """Save model checkpoint"""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'train_metrics': train_metrics,
            'val_metrics': val_metrics,
            'train_losses': self.train_losses,
            'val_losses': self.val_losses,
            'metrics_history': self.metrics_history
        }
        
        checkpoint_path = os.path.join(self.save_dir, f'checkpoint_epoch_{epoch}.pth')
        torch.save(checkpoint, checkpoint_path)
    
    def save_best_model(self, epoch, train_metrics, val_metrics):
        """Save best model"""
        best_model = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'train_metrics': train_metrics,
            'val_metrics': val_metrics
        }
        
        best_path = os.path.join(self.save_dir, 'best_model.pth')
        torch.save(best_model, best_path)
        print(f"  ✓ Saved best model at epoch {epoch}")
    
    def save_training_history(self):
        """Save training history as CSV"""
        df = pd.DataFrame(self.metrics_history)
        history_path = os.path.join(self.save_dir, 'training_history.csv')
        df.to_csv(history_path, index=False)
        print(f"Saved training history: {history_path}")
    
    def plot_training_curves(self):
        """Plot training and validation curves"""
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        
        epochs = range(len(self.train_losses))
        
        # Loss curves
        axes[0, 0].plot(epochs, self.train_losses, label='Training', alpha=0.8)
        if self.val_losses and any(x > 0 for x in self.val_losses):
            axes[0, 0].plot(epochs, self.val_losses, label='Validation', alpha=0.8)
        axes[0, 0].set_title('Loss Curves')
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('Loss')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # Accuracy curve
        if self.metrics_history:
            val_accs = [m['val_accuracy'] for m in self.metrics_history]
            axes[0, 1].plot(epochs, val_accs, color='green', alpha=0.8)
            axes[0, 1].set_title('Validation Accuracy')
            axes[0, 1].set_xlabel('Epoch')
            axes[0, 1].set_ylabel('Accuracy')
            axes[0, 1].grid(True, alpha=0.3)
        
        # Learning rate curve
        if self.metrics_history:
            lrs = [m['lr'] for m in self.metrics_history]
            axes[1, 0].plot(epochs, lrs, color='red', alpha=0.8)
            axes[1, 0].set_title('Learning Rate')
            axes[1, 0].set_xlabel('Epoch')
            axes[1, 0].set_ylabel('Learning Rate')
            axes[1, 0].set_yscale('log')
            axes[1, 0].grid(True, alpha=0.3)
        
        # Training time per epoch
        if self.metrics_history:
            times = [m['time'] for m in self.metrics_history]
            axes[1, 1].plot(epochs, times, color='orange', alpha=0.8)
            axes[1, 1].set_title('Training Time per Epoch')
            axes[1, 1].set_xlabel('Epoch')
            axes[1, 1].set_ylabel('Time (seconds)')
            axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        curves_path = os.path.join(self.save_dir, 'training_curves.png')
        plt.savefig(curves_path, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"Saved training curves: {curves_path}")
    
    def generate_comprehensive_embeddings(self, data_dict):
        """Generate comprehensive embeddings with metadata"""
        print("\nGenerating comprehensive CD4 embeddings...")
        
        self.model.eval()
        embeddings_dict = {}
        metadata = []
        
        with torch.no_grad():
            for subtype_id, (context, data) in enumerate(data_dict.items()):
                data = data.to(self.device)
                embeddings = self.model(data.x, data.edge_index, subtype_id=subtype_id)
                
                # Store embeddings
                embeddings_dict[context] = {
                    'embeddings': embeddings.cpu(),
                    'proteins': list(range(embeddings.size(0))),  # Placeholder for protein names
                    'subtype_id': subtype_id,
                    'num_proteins': embeddings.size(0),
                    'embedding_dim': embeddings.size(1)
                }
                
                # Metadata for analysis
                metadata.append({
                    'context': context,
                    'subtype_id': subtype_id,
                    'num_proteins': embeddings.size(0),
                    'num_edges': data.edge_index.size(1),
                    'avg_degree': 2 * data.edge_index.size(1) / embeddings.size(0)
                })
                
                print(f"  {context}: {embeddings.shape}")
        
        # Save embeddings
        embeddings_path = os.path.join(self.save_dir, 'cd4_embeddings.pth')
        torch.save(embeddings_dict, embeddings_path)
        
        # Save metadata
        metadata_df = pd.DataFrame(metadata)
        metadata_path = os.path.join(self.save_dir, 'embedding_metadata.csv')
        metadata_df.to_csv(metadata_path, index=False)
        
        print(f"Saved embeddings: {embeddings_path}")
        print(f"Saved metadata: {metadata_path}")
        
        return embeddings_dict, metadata_df

def main():
    """Main training pipeline"""
    print("="*80)
    print("CD4-PINNACLE: Full Training Pipeline")
    print("="*80)
    
    # Setup
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Load data
    print("\n1. Loading CD4 data...")
    data = read_cd4_data(
        global_ppi_f="../data/networks/global_ppi_edgelist.txt",
        ppi_dir="../data/networks/ppi_edgelists/",
        mg_f="../data/networks/cd4_mg_edgelist.txt"
    )
    
    print(f"Loaded {len(data['ppi_data'])} CD4 subtypes")
    
    # Create model
    print("\n2. Creating CD4-PINNACLE model...")
    model = CD4PinnacleSimple(
        input_dim=2048,
        hidden_dim=256,
        output_dim=128,
        num_subtypes=len(data['ppi_data']),
        n_heads=8,
        dropout=0.3
    )
    
    print(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # Create trainer
    print("\n3. Setting up trainer...")
    trainer = CD4FullTrainer(model, device)
    
    # Train model
    print("\n4. Training model...")
    training_history = trainer.train_with_validation(
        data['ppi_data'], 
        epochs=150, 
        log_every=15, 
        save_every=30
    )
    
    # Plot training curves
    print("\n5. Generating training visualizations...")
    trainer.plot_training_curves()
    
    # Generate embeddings
    print("\n6. Generating comprehensive embeddings...")
    embeddings, metadata = trainer.generate_comprehensive_embeddings(data['ppi_data'])
    
    # Summary
    print("\n" + "="*80)
    print("CD4-PINNACLE Training Summary")
    print("="*80)
    print(f"✓ Trained on {len(data['ppi_data'])} CD4 subtypes")
    print(f"✓ Generated embeddings for {sum(meta['num_proteins'] for meta in metadata)} proteins")
    print(f"✓ Average network size: {metadata['num_proteins'].mean():.1f} proteins")
    print(f"✓ Model saved in: {trainer.save_dir}")
    print(f"✓ Final training loss: {trainer.train_losses[-1]:.4f}")
    if trainer.val_losses and any(x > 0 for x in trainer.val_losses):
        print(f"✓ Final validation loss: {trainer.val_losses[-1]:.4f}")
    
    return trainer, embeddings, metadata

if __name__ == "__main__":
    trainer, embeddings, metadata = main()