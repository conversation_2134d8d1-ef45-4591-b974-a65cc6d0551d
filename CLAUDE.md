# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Environment Setup

Create the conda environment and install dependencies:
```bash
conda env create -f environment.yml
conda activate pinnacle
bash install_pyg.sh
```

For data preprocessing (single-cell analysis):
```bash
conda env create -f data_prep/scRNA_env.yml
conda activate scRNA_env
```

## Core Commands

### Training CD4-PINNACLE Model
```bash
cd pinnacle
python train.py \
    --G_f ../data/networks/global_ppi_edgelist.txt \
    --ppi_dir ../data/networks/ppi_edgelists/ \
    --mg_f ../data/networks/mg_edgelist.txt \
    --save_prefix ../data/pinnacle_embeds/
```

Use the optimized hyperparameters script:
```bash
cd pinnacle
bash run_pinnacle.sh
```

### Data Preprocessing Pipeline
```bash
# Step 1: Construct CD4 T cell subtype-specific PPI networks
cd data_prep
python 0.constructPPI.py -rank True -rank_pval_filename ../data/networks/cd4_ranked_genes
python 0.constructPPI.py -rank False -rank_pval_filename ../data/networks/cd4_ranked_genes -celltype_ppi_filename ../data/networks/cd4_celltype_ppi -max_pval 1 -max_num_genes 4000

# Step 2: Evaluate PPI networks
python 1.evaluatePPI.py -celltype_ppi ../data/networks/cd4_celltype_ppi

# Step 3: Prepare CellPhoneDB inputs
python 2.prepCellPhoneDB.py -data ../data/raw/CD4.h5ad -output ../data/cellphonedb/

# Step 4: Run CellPhoneDB (requires separate environment)
conda activate cpdb
bash 3.run_cellphonedb.sh

# Step 5: Construct cell-cell interaction networks
conda activate scRNA_env
python 4.constructCCI.py -cpdb_output ../data/cellphonedb/pvalues.txt -cci_edgelist ../data/networks/cci_edgelist.txt

# Step 6: Build metagraph
python 5.constructMG.py -celltype_ppi ../data/networks/cd4_celltype_ppi -cci_edgelist ../data/networks/cci_edgelist.txt -mg_edgelist ../data/networks/mg_edgelist.txt
```

### Fine-tuning for Therapeutic Target Prioritization
```bash
cd finetune_pinnacle
python train.py \
    --disease EFO_0000685 \
    --embeddings_dir ../data/pinnacle_embeds/
```

Use the provided script for multiple diseases:
```bash
bash run_model.sh
```

### Visualization and Evaluation
```bash
# Visualize learned representations
cd evaluate
python visualize_representations.py

# Evaluate therapeutic target prioritization
python evaluate_target_prioritization.py
```

## Architecture Overview

### Multi-Scale Geometric Deep Learning Model
CD4-PINNACLE operates on three hierarchical levels:

1. **CD4 T Cell Subtype-Specific PPI Networks**: 42 context-specific protein interaction networks based on the `annotation_correction_low` column from CD4.h5ad
2. **CD4 T Cell Contextualized Representations**: Cell subtype-aware protein embeddings with attention mechanisms
3. **T Cell Metagraph**: Hierarchical relationships between CD4 subtypes and their tissue contexts

### Key Components
- `pinnacle/model.py`: Main PINNACLE model with multi-head attention (PCTConv + PPIConv layers)
- `pinnacle/conv.py`: Graph convolution layers for protein interaction modeling
- `pinnacle/loss.py`: Multi-objective loss functions (PPI reconstruction, center loss, cross-entropy)
- `pinnacle/center_loss.py`: Center loss implementation for embedding clustering
- `pinnacle/generate_input.py`: Data preprocessing and graph construction utilities
- `pinnacle/minibatch_utils.py`: Efficient minibatching for large-scale graphs using GraphSAINT

### Data Pipeline Architecture
- `data_prep/0.constructPPI.py`: Build CD4 subtype-specific PPI networks from single-cell data
- `data_prep/4.constructCCI.py`: Construct cell-cell interaction networks using CellPhoneDB
- `data_prep/5.constructMG.py`: Build tissue-level metagraph connecting CD4 subtypes
- `data_config.py`: Centralized configuration for data paths and parameters

### Fine-tuning Framework
- `finetune_pinnacle/model.py`: MLP architectures for downstream task adaptation
- `finetune_pinnacle/train.py`: Training pipeline for therapeutic target prioritization
- `finetune_pinnacle/data_prep.py`: Data preprocessing for biomedical tasks
- `finetune_pinnacle/metrics_utils.py`: Evaluation metrics (AUROC, AUPRC, precision@K)

## CD4 T Cell Data Specifications

### Dataset Characteristics
- **Size**: 411,068 CD4 T cells with 31,787 genes
- **Subtypes**: 42 distinct CD4 T cell subtypes from `annotation_correction_low`
- **Major Subtypes**:
  - CD4 Tn (Naive): 112,500 cells
  - CD4 Th17: 37,912 cells  
  - CD4 Treg (Regulatory): 29,347 cells
  - CD4 Tstr Naive-Like: 22,506 cells
  - CD4 Tstr Cytotoxicity: 20,222 cells

### Key Annotation Column
Use `annotation_correction_low` as the authoritative cell type annotation. This provides the most refined CD4 T cell subtype classifications for context-aware protein embedding generation.

## Key Hyperparameters

### PINNACLE Training (pinnacle/parse_args.py)
- `--feat_mat`: Random feature matrix size (default: 2048)
- `--output`: Output embedding dimension (default: 8)
- `--hidden`: Hidden layer size (default: 16)
- `--n_heads`: Number of attention heads (default: 8)
- `--theta`: Weight for PPI loss (default: 0.1)
- `--lmbda`: Weight for center loss (default: 0.01)
- `--batch_size`: Minibatch size (default: 64)
- `--lr`: Learning rate (default: 0.001)
- `--epochs`: Training epochs (default: 300)

### Fine-tuning (finetune_pinnacle/train_utils.py)
- `--hidden_dim_1`: First hidden layer dimension
- `--hidden_dim_2`: Second hidden layer dimension
- `--dropout`: Dropout rate for regularization
- `--lr`: Learning rate for downstream tasks
- `--wd`: Weight decay for regularization

## Data Requirements

Expected data structure:
```
data/
├── raw/
│   └── CD4.h5ad                    # CD4 T cell single-cell data
├── networks/
│   ├── global_ppi_edgelist.txt     # Global reference PPI network
│   ├── ppi_edgelists/              # CD4 subtype-specific PPI networks
│   └── mg_edgelist.txt             # Metagraph connecting CD4 subtypes
└── pinnacle_embeds/                # Pre-trained model checkpoints
```

## CD4-PINNACLE Innovations

### Context-Aware Protein Embeddings
- Generate protein representations specific to CD4 T cell subtypes (Naive, Th17, Treg, etc.)
- Capture subtype-specific protein interactions and functional states
- Enable precise therapeutic target discovery for T cell-mediated diseases

### Multi-Scale Attention Mechanisms
- **Protein-level attention**: Focus on subtype-specific protein interactions
- **Cell-level attention**: Model relationships between CD4 subtypes
- **Tissue-level attention**: Capture tissue-specific T cell functions

### Biological Applications
- Autoimmune disease target prioritization (rheumatoid arthritis, IBD)
- Cancer immunotherapy target discovery
- T cell exhaustion and dysfunction analysis
- Regulatory T cell mechanism elucidation

## Special Notes

- Model uses GraphSAINT sampling for scalable training on large CD4 T cell graphs
- Context-aware embeddings generated for 42 CD4 T cell subtypes
- Framework supports zero-shot transfer to new CD4 subtypes and disease contexts
- Therapeutic target prioritization leverages OpenTargets disease-protein associations
- All CD4 subtype annotations use the `annotation_correction_low` column exclusively