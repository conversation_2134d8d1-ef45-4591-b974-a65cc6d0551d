name: pinnacle 
channels:
  - pytorch
  - plotly
  - biobuilds
  - anaconda
  - bokeh
  - conda-forge
  - bioconda
  - defaults
dependencies:
  - _libgcc_mutex=0.1
  - _openmp_mutex=5.1
  - biothings_client=0.2.6
  - blas=1.0
  - bleach=5.0.1
  - bokeh=2.4.3
  - bottleneck=1.3.5
  - brotlipy=0.7.0
  - bzip2=1.0.8
  - ca-certificates=2022.10.11
  - certifi=2022.9.24
  - cffi=1.15.1
  - charset-normalizer=2.0.4
  - click=8.1.3
  - cloudpickle=2.2.0
  - colorama=0.4.5
  - colorcet=3.0.0
  - cryptography=37.0.1
  - cudatoolkit=10.2.89
  - cycler=0.11.0
  - cytoolz=0.11.0
  - dask-core=2022.9.2
  - datashader=0.14.2
  - datashape=0.5.4
  - docker-pycreds=0.4.0
  - ffmpeg=4.2.2
  - fftw=3.3.10
  - freetype=2.11.0
  - fsspec=2022.8.2
  - giflib=5.2.1
  - gitdb=4.0.9
  - gitpython=3.1.29
  - gmp=6.2.1
  - gnutls=3.6.15
  - holoviews=1.15.1
  - icu=67.1
  - idna=3.3
  - imageio=2.19.3
  - importlib-metadata=4.11.4
  - intel-openmp=2021.4.0
  - jinja2=3.1.2
  - joblib=1.2.0
  - jpeg=9e
  - kiwisolver=1.4.2
  - lame=3.100
  - lcms2=2.12
  - ld_impl_linux-64=2.38
  - lerc=3.0
  - libdeflate=1.8
  - libffi=3.3
  - libgcc-ng=11.2.0
  - libgfortran-ng=12.1.0
  - libgfortran5=12.1.0
  - libgomp=11.2.0
  - libidn2=2.3.2
  - libllvm11=11.1.0
  - libopus=1.3.1
  - libpng=1.6.37
  - libprotobuf=3.15.8
  - libstdcxx-ng=11.2.0
  - libtasn1=4.16.0
  - libtiff=4.4.0
  - libunistring=0.9.10
  - libvpx=1.7.0
  - libwebp=1.2.4
  - libwebp-base=1.2.4
  - llvmlite=0.38.0
  - locket=1.0.0
  - lz4-c=1.9.3
  - markdown=3.4.1
  - markupsafe=2.1.1
  - matplotlib=3.2.2
  - matplotlib-base=3.2.2
  - mkl=2021.4.0
  - mkl-service=2.4.0
  - mkl_fft=1.3.1
  - mkl_random=1.2.2
  - multipledispatch=0.6.0
  - mygene=3.2.2
  - ncurses=6.3
  - nettle=3.7.3
  - networkx=2.8.4
  - numba=0.55.1
  - numexpr=2.8.3
  - numpy=1.21.5
  - numpy-base=1.21.5
  - obonet=0.2.3
  - openh264=2.1.1
  - openssl=1.1.1q
  - packaging=21.3
  - pandas=1.4.3
  - panel=0.14.0
  - param=1.12.2
  - partd=1.3.0
  - pathtools=0.1.2
  - pillow=9.2.0
  - pip=22.2.2
  - plotly=5.10.0
  - promise=2.3
  - protobuf=3.15.8
  - psutil=5.9.0
  - pycparser=2.21
  - pyct=0.4.6
  - pyct-core=0.4.6
  - pynndescent=0.5.7
  - pyopenssl=22.0.0
  - pysocks=1.7.1
  - python=3.8.13
  - python-dateutil=2.8.2
  - python_abi=3.8
  - pytorch=1.12.1
  - pytorch-mutex=1.0
  - pytz=2022.1
  - pyviz_comms=2.2.1
  - pywavelets=1.3.0
  - pyyaml=6.0
  - readline=8.1.2
  - requests=2.28.1
  - scikit-image=0.19.2
  - scikit-learn=1.1.2
  - seaborn=0.11.2
  - sentry-sdk=1.9.10
  - setproctitle=1.2.2
  - setuptools=63.4.1
  - shortuuid=1.0.9
  - six=1.16.0
  - smmap=3.0.5
  - sqlite=3.39.3
  - tbb=2021.6.0
  - tenacity=8.0.1
  - threadpoolctl=3.1.0
  - tifffile=2020.10.1
  - tk=8.6.12
  - toolz=0.12.0
  - torchaudio=0.12.1
  - torchvision=0.13.1
  - tornado=6.1
  - tqdm=4.64.1
  - typing_extensions=4.3.0
  - umap-learn=0.5.3
  - urllib3=1.26.11
  - wandb=0.13.4
  - webencodings=0.5.1
  - wheel=0.37.1
  - x264=1!157.20191217
  - xarray=2022.9.0
  - xz=5.2.6
  - yaml=0.2.5
  - zipp=3.9.0
  - zlib=1.2.12
  - zstd=1.5.2
  - pip:
    - kaleido==0.2.1
    - littleutils==0.2.2
    - outdated==0.2.2
    - pyparsing==3.0.9
    - scipy==1.9.2
prefix: ~/miniconda3/envs/pinnacle
