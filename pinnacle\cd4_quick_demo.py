#!/usr/bin/env python3
"""
Quick CD4-PINNACLE Demo
Streamlined training for demonstration
"""

import os
import numpy as np
import pandas as pd
import torch
import matplotlib.pyplot as plt
from datetime import datetime

from cd4_generate_input import read_cd4_data
from cd4_model_simple import CD4PinnacleSimple, CD4PinnacleTrainer

def quick_training_demo():
    """Quick training demonstration"""
    print("="*60)
    print("CD4-PINNACLE Quick Training Demo")
    print("="*60)
    
    # Setup
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Device: {device}")
    
    # Load data (subset for demo)
    print("\n1. Loading CD4 data...")
    data = read_cd4_data(
        global_ppi_f="../data/networks/global_ppi_edgelist.txt",
        ppi_dir="../data/networks/ppi_edgelists/",
        mg_f="../data/networks/cd4_mg_edgelist.txt"
    )
    
    # Select subset of subtypes for quick demo
    demo_subtypes = list(data['ppi_data'].keys())[:10]  # First 10 subtypes
    demo_data = {k: v for k, v in data['ppi_data'].items() if k in demo_subtypes}
    
    print(f"Demo with {len(demo_data)} CD4 subtypes:")
    for i, (context, ppi_data) in enumerate(demo_data.items()):
        print(f"  {i+1}. {context}: {ppi_data.x.size(0)} proteins, {ppi_data.edge_index.size(1)} edges")
    
    # Create model
    print("\n2. Creating model...")
    model = CD4PinnacleSimple(
        input_dim=2048,
        hidden_dim=128,
        output_dim=64,
        num_subtypes=len(demo_data),
        n_heads=4,
        dropout=0.3
    )
    
    # Create trainer
    trainer = CD4PinnacleTrainer(model, device)
    
    # Quick training
    print("\n3. Quick training (30 epochs)...")
    trainer.train(demo_data, epochs=30, log_every=5)
    
    # Generate embeddings
    print("\n4. Generating embeddings...")
    embeddings = trainer.generate_embeddings(demo_data)
    
    # Analysis
    print("\n5. Quick analysis...")
    embedding_stats = []
    for context, emb in embeddings.items():
        stats = {
            'context': context.replace('cluster:CD4 ', ''),
            'num_proteins': emb.size(0),
            'embedding_dim': emb.size(1),
            'mean_norm': torch.norm(emb, dim=1).mean().item(),
            'std_norm': torch.norm(emb, dim=1).std().item()
        }
        embedding_stats.append(stats)
    
    stats_df = pd.DataFrame(embedding_stats)
    print("\nEmbedding Statistics:")
    print(stats_df.to_string(index=False))
    
    # Save results
    results_dir = "../results/cd4_demo"
    os.makedirs(results_dir, exist_ok=True)
    
    # Save embeddings
    torch.save(embeddings, os.path.join(results_dir, 'demo_embeddings.pth'))
    stats_df.to_csv(os.path.join(results_dir, 'embedding_stats.csv'), index=False)
    
    # Create summary visualization
    fig, axes = plt.subplots(1, 2, figsize=(12, 5))
    
    # Network sizes
    axes[0].bar(range(len(stats_df)), stats_df['num_proteins'])
    axes[0].set_title('CD4 Subtype Network Sizes')
    axes[0].set_xlabel('Subtype Index')
    axes[0].set_ylabel('Number of Proteins')
    axes[0].grid(True, alpha=0.3)
    
    # Embedding norms
    axes[1].bar(range(len(stats_df)), stats_df['mean_norm'])
    axes[1].set_title('Average Embedding Norms')
    axes[1].set_xlabel('Subtype Index')
    axes[1].set_ylabel('Mean L2 Norm')
    axes[1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(os.path.join(results_dir, 'demo_summary.png'), dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"\n✓ Demo completed! Results saved in: {results_dir}")
    print(f"✓ Generated embeddings for {sum(stats_df['num_proteins'])} proteins")
    print(f"✓ Average embedding dimension: {stats_df['embedding_dim'].iloc[0]}")
    
    return trainer, embeddings, stats_df

if __name__ == "__main__":
    trainer, embeddings, stats = quick_training_demo()