#!/usr/bin/env python3
"""
CD4-specific input generation for PINNACLE training
Adapted for CD4 T cell subtype networks
"""

import glob
import os
from collections import Counter
import pandas as pd
import numpy as np
import random
import networkx as nx
import torch
from torch_geometric.data import Data

def set_random_seeds(seed=42):
    """Set random seeds for reproducibility"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)

def split_data(num_y):
    """Split data into train/val/test"""
    split_idx = list(range(num_y))
    random.shuffle(split_idx)
    train_idx = split_idx[:int(len(split_idx) * 0.8)]
    train_mask = torch.zeros(num_y, dtype=torch.bool)
    train_mask[train_idx] = 1
    
    val_idx = split_idx[int(len(split_idx) * 0.8):int(len(split_idx) * 0.9)]
    val_mask = torch.zeros(num_y, dtype=torch.bool)
    val_mask[val_idx] = 1
    
    test_idx = split_idx[int(len(split_idx) * 0.9):]
    test_mask = torch.zeros(num_y, dtype=torch.bool)
    test_mask[test_idx] = 1
    
    return train_mask, val_mask, test_mask

def read_cd4_ppi(ppi_dir):
    """Read CD4 subtype-specific PPI networks"""
    orig_ppi_layers = dict()
    ppi_layers = dict()
    ppi_train = dict()
    ppi_val = dict()
    ppi_test = dict()
    
    print(f"Reading PPI networks from: {ppi_dir}")
    
    # Read all CD4 PPI network files
    ppi_files = glob.glob(os.path.join(ppi_dir, "CD4_*.txt"))
    print(f"Found {len(ppi_files)} CD4 PPI network files")
    
    for f in ppi_files:
        # Parse context name from filename
        filename = os.path.basename(f)
        context = filename.replace('.txt', '').replace('CD4_', '')
        context = f"cluster:CD4 {context.replace('_', ' ')}"
        
        print(f"Processing: {context}")
        
        # Read edgelist
        try:
            ppi = nx.read_edgelist(f, delimiter='\t')
            
            if len(ppi.nodes()) == 0:
                print(f"  Warning: Empty network for {context}")
                continue
                
            # Check if network is connected
            if not nx.is_connected(ppi):
                # Get largest connected component
                largest_cc = max(nx.connected_components(ppi), key=len)
                ppi = ppi.subgraph(largest_cc).copy()
                print(f"  Using largest connected component: {len(ppi.nodes())} nodes")
            
            # Relabel nodes to integers
            mapping = {n: idx for idx, n in enumerate(ppi.nodes())}
            ppi_layers[context] = nx.relabel_nodes(ppi, mapping)
            orig_ppi_layers[context] = ppi
            
            # Split into train/val/test
            num_edges = len(ppi_layers[context].edges)
            ppi_train[context], ppi_val[context], ppi_test[context] = split_data(num_edges)
            
            print(f"  Nodes: {len(ppi.nodes())}, Edges: {num_edges}")
            
        except Exception as e:
            print(f"  Error reading {f}: {e}")
            continue
    
    print(f"Successfully loaded {len(ppi_layers)} CD4 PPI networks")
    return orig_ppi_layers, ppi_layers, ppi_train, ppi_val, ppi_test

def create_data(G, train_mask, val_mask, test_mask, node_type, edge_type, x):
    """Create PyTorch Geometric Data object"""
    edge_index = torch.tensor(list(G.edges)).t().contiguous()
    y = torch.ones(edge_index.size(1))
    num_classes = len(torch.unique(y))
    node_type = torch.tensor(node_type)
    edge_type = torch.tensor(edge_type)
    
    data = Data(
        x=x, 
        y=y, 
        num_classes=num_classes, 
        edge_index=edge_index, 
        node_type=node_type, 
        edge_attr=edge_type, 
        train_mask=train_mask, 
        val_mask=val_mask, 
        test_mask=test_mask
    )
    return data

def read_cd4_data(global_ppi_f, ppi_dir, mg_f, feat_mat_dim=2048):
    """Main function to read CD4 data for PINNACLE training"""
    
    set_random_seeds(42)
    
    print("=== Loading CD4-PINNACLE Data ===")
    
    # Read global PPI network
    print(f"Loading global PPI from: {global_ppi_f}")
    G = nx.read_edgelist(global_ppi_f, delimiter=' ')
    print(f"Global PPI: {len(G.nodes)} nodes, {len(G.edges)} edges")
    
    # Create random feature matrix for global proteins
    feat_mat = torch.normal(torch.zeros(len(G.nodes), feat_mat_dim), std=1)
    
    # Read CD4 subtype-specific PPI networks
    orig_ppi_layers, ppi_layers, ppi_train, ppi_val, ppi_test = read_cd4_ppi(ppi_dir)
    
    # Read CD4 metagraph
    print(f"Loading CD4 metagraph from: {mg_f}")
    metagraph = nx.read_edgelist(mg_f, data=False, delimiter="\t", create_using=nx.DiGraph)
    
    if not nx.is_connected(metagraph.to_undirected()):
        print("Warning: Metagraph is not connected, using largest component")
        largest_cc = max(nx.connected_components(metagraph.to_undirected()), key=len)
        metagraph = metagraph.subgraph(largest_cc).copy()
    
    print(f"Metagraph: {len(metagraph.nodes)} nodes, {len(metagraph.edges)} edges")
    
    # Create metagraph feature matrix
    mg_feat_mat = torch.zeros(len(metagraph.nodes), feat_mat_dim)
    
    # Create mapping for metagraph nodes
    mg_mapping = {n: i for i, n in enumerate(sorted(metagraph.nodes))}
    metagraph = nx.relabel_nodes(metagraph, mg_mapping)
    
    # Create PyTorch Geometric data objects for PPI networks
    ppi_data = {}
    for context in ppi_layers:
        num_nodes = len(ppi_layers[context].nodes)
        x = torch.normal(torch.zeros(num_nodes, feat_mat_dim), std=1)
        node_type = torch.zeros(num_nodes, dtype=torch.long)
        edge_type = torch.zeros(len(ppi_layers[context].edges), dtype=torch.long)
        
        ppi_data[context] = create_data(
            ppi_layers[context], 
            ppi_train[context], 
            ppi_val[context], 
            ppi_test[context],
            node_type, 
            edge_type, 
            x
        )
    
    # Create metagraph data
    mg_train, mg_val, mg_test = split_data(len(metagraph.edges))
    mg_node_type = torch.zeros(len(metagraph.nodes), dtype=torch.long)
    mg_edge_type = torch.zeros(len(metagraph.edges), dtype=torch.long)
    
    mg_data = create_data(
        metagraph, 
        mg_train, 
        mg_val, 
        mg_test,
        mg_node_type, 
        mg_edge_type, 
        mg_feat_mat
    )
    
    print(f"✓ Created data for {len(ppi_data)} CD4 subtypes")
    
    return {
        'global_ppi': G,
        'ppi_data': ppi_data,
        'mg_data': mg_data,
        'orig_ppi_layers': orig_ppi_layers,
        'ppi_layers': ppi_layers,
        'metagraph': metagraph,
        'feat_mat': feat_mat,
        'mg_feat_mat': mg_feat_mat
    }

def get_metapaths(data_dict):
    """Generate metapaths for CD4 subtypes"""
    metapaths = []
    
    # Simple metapaths connecting CD4 subtypes
    mg_nodes = list(data_dict['metagraph'].nodes())
    
    # Add sequential metapaths
    for i in range(len(mg_nodes) - 1):
        metapaths.append([mg_nodes[i], mg_nodes[i+1]])
    
    # Add reverse metapaths
    for i in range(len(mg_nodes) - 1, 0, -1):
        metapaths.append([mg_nodes[i], mg_nodes[i-1]])
    
    return metapaths

def get_centerloss_labels(embeddings, batch_data):
    """Generate center loss labels for CD4 subtypes"""
    # Simple implementation - assign sequential labels
    num_nodes = embeddings.shape[0]
    labels = torch.arange(num_nodes) % len(batch_data)
    return labels

if __name__ == "__main__":
    # Test the data loading
    data = read_cd4_data(
        global_ppi_f="../data/networks/global_ppi_edgelist.txt",
        ppi_dir="../data/networks/ppi_edgelists/",
        mg_f="../data/networks/cd4_mg_edgelist.txt"
    )
    
    print(f"Data loaded successfully!")
    print(f"CD4 subtypes: {list(data['ppi_data'].keys())}")