name: cpdb
channels:
  - r
  - defaults
dependencies:
  - _libgcc_mutex=0.1
  - _openmp_mutex=5.1
  - _r-mutex=1.0.0
  - binutils_impl_linux-64=2.33.1
  - binutils_linux-64=2.33.1
  - bwidget=1.9.11
  - bzip2=1.0.8
  - ca-certificates=2022.07.19
  - cairo=1.16.0
  - certifi=2022.9.14
  - curl=7.69.1
  - fontconfig=2.13.1
  - freetype=2.11.0
  - fribidi=1.0.10
  - gcc_impl_linux-64=7.3.0
  - gcc_linux-64=7.3.0
  - gfortran_impl_linux-64=7.3.0
  - gfortran_linux-64=7.3.0
  - glib=2.69.1
  - graphite2=1.3.14
  - gsl=2.4
  - gxx_impl_linux-64=7.3.0
  - gxx_linux-64=7.3.0
  - harfbuzz=4.3.0
  - icu=58.2
  - jpeg=9e
  - krb5=1.17.1
  - ld_impl_linux-64=2.33.1
  - lerc=3.0
  - libcurl=7.69.1
  - libdeflate=1.8
  - libedit=3.1.20210910
  - libffi=3.3
  - libgcc-ng=11.2.0
  - libgfortran-ng=7.5.0
  - libgfortran4=7.5.0
  - libgomp=11.2.0
  - libpng=1.6.37
  - libssh2=1.10.0
  - libstdcxx-ng=11.2.0
  - libtiff=4.4.0
  - libuuid=1.0.3
  - libwebp-base=1.2.2
  - libxcb=1.15
  - libxml2=2.9.14
  - lz4-c=1.9.3
  - make=4.2.1
  - ncurses=6.3
  - openssl=1.1.1q
  - pango=1.50.7
  - pcre=8.45
  - pip=22.2.2
  - pixman=0.40.0
  - python=3.7.13
  - r=3.6.0
  - r-base=3.6.1
  - r-boot=1.3_20
  - r-class=7.3_15
  - r-cluster=2.0.8
  - r-codetools=0.2_16
  - r-foreign=0.8_71
  - r-kernsmooth=2.23_15
  - r-lattice=0.20_38
  - r-mass=7.3_51.3
  - r-matrix=1.2_17
  - r-mgcv=1.8_28
  - r-nlme=3.1_139
  - r-nnet=7.3_12
  - r-recommended=3.6.0
  - r-rpart=4.1_15
  - r-spatial=7.3_11
  - r-survival=2.44_1.1
  - readline=8.1.2
  - sqlite=3.39.3
  - tk=8.6.12
  - tktable=2.10
  - wheel=0.37.1
  - xz=5.2.6
  - zlib=1.2.12
  - zstd=1.5.2
  - pip:
    - aniso8601==9.0.1
    - anndata==0.7.8
    - backports-zoneinfo==0.2.1
    - boto3==1.24.84
    - botocore==1.27.84
    - cellphonedb==3.1.0
    - cffi==1.15.1
    - charset-normalizer==2.1.1
    - click==7.1.2
    - fbpca==1.0
    - flask==1.1.4
    - flask-restful==0.3.9
    - flask-testing==0.8.1
    - geosketch==0.3
    - h5py==2.10.0
    - idna==3.4
    - importlib-metadata==5.0.0
    - itsdangerous==1.1.0
    - jinja2==2.11.3
    - jmespath==1.0.1
    - joblib==1.2.0
    - llvmlite==0.39.1
    - markupsafe==2.1.1
    - natsort==8.2.0
    - numba==0.56.2
    - numpy==1.19.5
    - numpy-groupies==0.9.19
    - packaging==21.3
    - pandas==1.1.4
    - pika==1.3.0
    - pycparser==2.21
    - pyparsing==3.0.9
    - python-dateutil==2.8.2
    - pytz==2022.4
    - pytz-deprecation-shim==0.1.0.post0
    - pyyaml==5.4.1
    - requests==2.28.1
    - rpy2==3.5.4
    - s3transfer==0.6.0
    - scikit-learn==0.22
    - scipy==1.7.3
    - setuptools==59.8.0
    - six==1.16.0
    - sqlalchemy==1.3.24
    - tqdm==4.64.1
    - typing-extensions==4.3.0
    - tzdata==2022.4
    - tzlocal==4.2
    - urllib3==1.26.12
    - werkzeug==1.0.1
    - xlrd==1.2.0
    - zipp==3.8.1
prefix: /home/<USER>/miniconda3/envs/cpdb
