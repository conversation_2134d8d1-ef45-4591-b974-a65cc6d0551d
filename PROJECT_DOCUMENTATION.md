# CD4-PINNACLE Project Documentation

## Project Overview

**CD4-PINNACLE** is a specialized geometric deep learning model that generates context-aware protein representations for CD4 T cell subtypes. This project extends the original PINNACLE framework to focus specifically on CD4 T cell biology, enabling precision immunotherapy target discovery through high-resolution protein interaction modeling.

### Key Innovations

1. **CD4 Subtype-Specific Networks**: 42 distinct CD4 T cell subtype-specific protein interaction networks
2. **Enhanced Resolution**: From 1-2 generic T cell types to 42 functionally distinct CD4 subtypes
3. **Disease-Relevant States**: Includes exhausted, regulatory, cytotoxic, and tissue-resident CD4 states
4. **Clinical Translation**: Direct application to immunotherapy target prioritization

---

## Project Structure

```
CD4_PINNACLE_project/
├── CLAUDE.md                           # Development guidance for Claude Code
├── Enhanced_CD4T_Research_Design.md    # Comprehensive research methodology
├── PROJECT_DOCUMENTATION.md            # This file
├── environment.yml                     # Conda environment specification
├── data_config.py                     # Centralized data configuration
├── run_cd4_preprocessing.sh           # Complete preprocessing pipeline
│
├── data/                              # Data directory
│   ├── raw/
│   │   └── CD4.h5ad                  # CD4 T cell single-cell data (411K cells)
│   ├── networks/                     # Generated networks
│   │   ├── global_ppi_edgelist.txt  # Global PPI reference
│   │   ├── cd4_celltype_ppi*.csv    # CD4 subtype-specific networks
│   │   └── mg_edgelist.txt          # CD4 metagraph
│   └── pinnacle_embeds/              # Model outputs and embeddings
│
├── data_prep/                         # Data preprocessing scripts
│   ├── cd4_constructPPI.py           # CD4-specific PPI network construction
│   ├── 0.constructPPI.py             # Original PPI construction
│   ├── 1.evaluatePPI.py              # Network evaluation
│   ├── 2.prepCellPhoneDB.py          # CellPhoneDB preparation
│   ├── 3.run_cellphonedb.sh          # CellPhoneDB execution
│   ├── 4.constructCCI.py             # Cell-cell interaction networks
│   ├── 5.constructMG.py              # Metagraph construction
│   └── utils.py                      # Utility functions
│
├── pinnacle/                          # Model implementation
│   ├── cd4_train.py                  # CD4-specific training script
│   ├── model.py                      # PINNACLE model architecture
│   ├── conv.py                       # Graph convolution layers
│   ├── loss.py                       # Loss functions
│   ├── center_loss.py                # Center loss implementation
│   ├── generate_input.py             # Data loading utilities
│   ├── parse_args.py                 # Argument parsing
│   └── run_pinnacle.sh               # Training execution script
│
├── finetune_pinnacle/                 # Fine-tuning framework
│   ├── train.py                      # Fine-tuning script
│   ├── model.py                      # Downstream task models
│   ├── data_prep.py                  # Data preprocessing for tasks
│   └── run_model.sh                  # Fine-tuning execution
│
├── evaluate/                          # Evaluation framework
│   ├── cd4_evaluation_framework.py   # Comprehensive evaluation suite
│   ├── visualize_representations.py  # Embedding visualization
│   └── evaluate_target_prioritization.py  # Target evaluation
│
└── notebooks/                         # Analysis notebooks
    └── 1.cd4_data_analysis.ipynb     # Comprehensive data analysis
```

---

## Quick Start Guide

### 1. Environment Setup

```bash
# Create conda environment
conda env create -f environment.yml
conda activate pinnacle

# Install PyTorch Geometric
bash install_pyg.sh

# Create scRNA-seq environment for preprocessing
conda env create -f data_prep/scRNA_env.yml
```

### 2. Data Preprocessing

```bash
# Run complete preprocessing pipeline
chmod +x run_cd4_preprocessing.sh
./run_cd4_preprocessing.sh
```

### 3. Model Training

```bash
# Train CD4-PINNACLE model
cd pinnacle
python cd4_train.py \
    --epochs 300 \
    --save_prefix ../data/pinnacle_embeds/cd4_pinnacle \
    --wandb_project cd4-pinnacle
```

### 4. Evaluation

```bash
# Run comprehensive evaluation
cd evaluate
python cd4_evaluation_framework.py
```

### 5. Fine-tuning for Therapeutic Targets

```bash
# Fine-tune for specific diseases
cd finetune_pinnacle
python train.py \
    --disease EFO_0000685 \
    --embeddings_dir ../data/pinnacle_embeds/
```

---

## Dataset Specifications

### CD4 T Cell Data Characteristics

| Metric | Value | Description |
|--------|-------|-------------|
| **Total Cells** | 411,068 | CD4 T cells from multiple studies |
| **Genes** | 31,787 | Full transcriptome coverage |
| **CD4 Subtypes** | 42 | High-resolution functional classification |
| **Annotation Column** | `annotation_correction_low` | Authoritative subtype labels |
| **Data Source** | Single-cell RNA-seq | Multi-study integration |

### Major CD4 Functional Categories

| Category | Cell Count | Percentage | Clinical Relevance |
|----------|------------|------------|-------------------|
| **Naive (Tn)** | 112,500 | 27.4% | Vaccine responses, immune aging |
| **Th17** | 37,912 | 9.2% | Autoimmunity, tissue inflammation |
| **Regulatory (Treg)** | 29,347 | 7.1% | Immune tolerance, cancer |
| **Stressed (Tstr)** | 42,728 | 10.4% | Disease pathology, dysfunction |
| **Memory (Tcm/Tem)** | 25,132 | 6.1% | Long-term immunity, recall |
| **Cytotoxic** | 19,285 | 4.7% | Tumor elimination, cytotoxicity |
| **Exhausted (Tex)** | 12,881 | 3.1% | Immunotherapy targets |
| **Other Subtypes** | 131,283 | 32.0% | Specialized functional states |

### Data Quality Metrics

- **Minimum cells per subtype**: 100 (for network construction)
- **Valid subtypes for modeling**: 35-40 (after QC filtering)
- **Average genes per cell**: ~2,500
- **Mitochondrial content**: <20%
- **Doublet rate**: <5%

---

## Model Architecture

### CD4-PINNACLE Framework

```
Input Layer:
├── CD4 Subtype-Specific PPI Networks (42 networks)
├── Global PPI Reference (15,461 proteins, 207,641 interactions)
└── CD4 Metagraph (subtype relationships)

Multi-Scale Processing:
├── Level 1: Protein-Protein Interactions
│   ├── PCTConv: Protein-Cell-Tissue convolution
│   ├── PPIConv: PPI-specific convolution
│   └── Multi-head attention (8 heads)
├── Level 2: CD4 Subtype Relationships
│   ├── Cell-cell attention mechanisms
│   └── Hierarchical subtype modeling
└── Level 3: Tissue Context Integration
    ├── Tissue-specific CD4 distributions
    └── Cross-tissue subtype variations

Output Layer:
├── Protein Embeddings: 8D per protein per subtype
├── CD4 Subtype Embeddings: Functional state representations
└── Tissue Embeddings: Tissue-specific CD4 contexts
```

### Key Model Components

1. **PCTConv (Protein-Cell-Tissue Convolution)**
   - Multi-head attention across protein interactions
   - Cell type-specific message passing
   - Tissue context integration

2. **PPIConv (PPI Convolution)**
   - Protein interaction-specific processing
   - Attention-weighted aggregation
   - Subtype-aware protein representations

3. **Multi-Objective Loss Function**
   ```
   L_total = θ·L_PPI + λ·L_center + α·L_classification
   
   Where:
   - L_PPI: Link prediction on CD4 networks (θ=0.15)
   - L_center: CD4 subtype separation (λ=0.02)
   - L_classification: Subtype identification (α=0.1)
   ```

### Hyperparameters (CD4-Optimized)

| Parameter | Value | Rationale |
|-----------|-------|-----------|
| **Embedding Dim** | 8 | Balance between expressiveness and efficiency |
| **Hidden Size** | 16 | Sufficient for CD4 complexity |
| **Attention Heads** | 8 | Capture diverse interaction patterns |
| **Dropout** | 0.3 | Prevent overfitting to abundant subtypes |
| **Learning Rate** | 0.001 | Stable convergence |
| **Batch Size** | 64 | Memory-efficient training |
| **Max Epochs** | 300 | Allow full convergence |
| **Early Stopping** | 30 epochs | Prevent overtraining |

---

## Biological Applications

### 1. Autoimmune Disease Target Discovery

**Rheumatoid Arthritis (RA)**
- **Target Subtypes**: CD4 Th17, CD4 Th1, CD4 Treg
- **Key Proteins**: TNF, IL6, IL17A, CTLA4
- **Approach**: Identify subtype-specific inflammatory mediators
- **Clinical Impact**: Precision anti-inflammatory therapies

**Inflammatory Bowel Disease (IBD)**
- **Target Subtypes**: CD4 Th17, CD4 Treg, CD4 Trm (tissue-resident)
- **Key Proteins**: IL23A, IL17A, FOXP3, integrin pathways
- **Approach**: Target gut-specific CD4 dysfunction
- **Clinical Impact**: Tissue-specific therapeutic interventions

### 2. Cancer Immunotherapy Enhancement

**Checkpoint Inhibition Optimization**
- **Target Subtypes**: CD4 Tex (exhausted), CD4 Treg
- **Key Proteins**: PDCD1, CTLA4, TIGIT, LAG3, FOXP3
- **Approach**: Enhance exhausted CD4 reactivation
- **Clinical Impact**: Improved checkpoint inhibitor responses

**CAR-T Cell Engineering**
- **Target Subtypes**: CD4 Tcm (central memory), CD4 Th1
- **Key Proteins**: CD62L, CCR7, IL2, IFNG
- **Approach**: Optimize CD4 helper function
- **Clinical Impact**: Enhanced CAR-T persistence and efficacy

### 3. Vaccine Design and Immune Aging

**Vaccine Response Optimization**
- **Target Subtypes**: CD4 Tn (naive), CD4 Tcm, CD4 Tfh
- **Key Proteins**: CD4, TCR components, BCL6, IL21
- **Approach**: Enhance naive-to-memory transition
- **Clinical Impact**: Improved vaccine efficacy in elderly

---

## Technical Implementation

### Data Preprocessing Pipeline

1. **Quality Control**
   ```bash
   # Cell filtering: >200 genes, <20% mitochondrial
   # Gene filtering: >3 cells expressing
   # Doublet removal: computational detection
   ```

2. **CD4 Subtype Networks**
   ```bash
   # Gene ranking: Wilcoxon rank-sum test per subtype
   # Top genes: 3000-4000 per subtype (p < 0.05)
   # PPI mapping: Map to global reference network
   # Network extraction: Largest connected component
   ```

3. **Cell-Cell Interactions**
   ```bash
   # CellPhoneDB: Ligand-receptor analysis
   # Statistical testing: Permutation-based p-values
   # Network construction: Significant interactions only
   ```

4. **Metagraph Construction**
   ```bash
   # Subtype relationships: Developmental hierarchies
   # Tissue integration: Tissue-specific distributions
   # Clinical mapping: Disease association scoring
   ```

### Training Configuration

**Hardware Requirements**
- **GPU**: NVIDIA V100/A100 (16-32GB VRAM)
- **RAM**: 64-128GB for large-scale processing
- **Storage**: 1TB+ for data and checkpoints
- **Compute**: 4-8 GPU cluster for parallel training

**Software Dependencies**
```yaml
python: 3.8+
pytorch: 1.12+
torch-geometric: 2.0+
scanpy: 1.9+
pandas: 1.4+
numpy: 1.21+
scikit-learn: 1.1+
matplotlib: 3.5+
seaborn: 0.11+
```

### Model Checkpointing and Reproducibility

```bash
# Automatic checkpointing every epoch
# Best model selection based on validation loss
# Full reproducibility with fixed random seeds
# Comprehensive logging with Weights & Biases
```

---

## Evaluation Framework

### 1. Embedding Quality Assessment

**Quantitative Metrics**
- **Subtype Separability**: Inter-subtype cosine distances
- **Protein Clustering**: Silhouette scores within subtypes
- **Dimensionality**: PCA explained variance ratios
- **Stability**: Cross-validation consistency

**Visualization Methods**
- **UMAP/t-SNE**: 2D embedding projections
- **Heatmaps**: Subtype similarity matrices
- **Network Plots**: Protein interaction networks
- **Functional Analysis**: GO term enrichment

### 2. Biological Validation

**Functional Consistency**
- **Protein Families**: Within-family similarity scores
- **Pathway Analysis**: KEGG/Reactome enrichment
- **Literature Validation**: Known interaction recovery
- **Cross-Species**: Conservation analysis

**Experimental Validation**
- **Protein Interactions**: Co-immunoprecipitation validation
- **Functional Assays**: Flow cytometry confirmation
- **Clinical Correlation**: Patient sample analysis
- **Drug Response**: Therapeutic target validation

### 3. Clinical Performance

**Therapeutic Target Metrics**
- **AUROC**: Target vs. non-target classification
- **Precision@K**: Top-K target enrichment
- **Clinical Relevance**: Literature-based scoring
- **Druggability**: Structural assessments

**Disease-Specific Evaluation**
- **Autoimmune Diseases**: Known target recovery
- **Cancer Immunotherapy**: Checkpoint molecule prioritization
- **Infectious Diseases**: Pathogen-specific responses
- **Aging**: Age-related CD4 dysfunction

---

## Results and Impact

### Expected Outcomes

**Scientific Contributions**
1. **First CD4 Subtype-Specific Protein Atlas**: 42 high-resolution networks
2. **Novel Therapeutic Targets**: 10-20 CD4-specific candidates
3. **Mechanistic Insights**: Subtype-specific protein functions
4. **Clinical Biomarkers**: Predictive signatures for therapy response

**Technical Advances**
1. **Scalable Graph Neural Networks**: Efficient multi-scale processing
2. **Context-Aware Embeddings**: Cell type-specific representations
3. **Multi-Modal Integration**: scRNA-seq + protein interaction data
4. **Open-Source Platform**: Community resource for immunology

**Clinical Translation**
1. **Precision Immunotherapy**: Subtype-specific targeting strategies
2. **Patient Stratification**: CD4 subtype-based biomarkers
3. **Drug Development**: Novel target identification pipeline
4. **Adverse Event Prevention**: Off-target effect prediction

### Publication Strategy

**High-Impact Publications**
1. **Nature/Science**: Main CD4-PINNACLE methodology paper
2. **Nature Methods**: Technical implementation and benchmarking
3. **Cell**: Biological insights and disease applications
4. **Nature Medicine**: Clinical validation and therapeutic targets

**Conference Presentations**
- **NeurIPS**: Machine learning methodology
- **ICML**: Graph neural network innovations
- **ISMB**: Computational biology applications
- **AAI**: Immunology findings and clinical relevance

### Commercial Applications

**Intellectual Property**
- **Core Algorithm**: CD4-PINNACLE methodology
- **Therapeutic Targets**: Novel protein candidates
- **Biomarker Panels**: Diagnostic and prognostic signatures
- **Software Platform**: Commercial implementation

**Industry Partnerships**
- **Pharmaceutical Companies**: Drug discovery collaborations
- **Biotech Startups**: Technology licensing
- **Diagnostics Companies**: Biomarker development
- **CROs**: Clinical validation services

---

## Future Directions

### Short-Term Extensions (1-2 years)

1. **Multi-Cell Type Models**
   - Extend to CD8 T cells, B cells, NK cells
   - Integrated immune cell interaction networks
   - Cross-cell type therapeutic targeting

2. **Disease-Specific Models**
   - Cancer-specific CD4 dysfunction models
   - Autoimmune disease specialized networks
   - Infectious disease response modeling

3. **Temporal Dynamics**
   - Time-course CD4 differentiation modeling
   - Disease progression tracking
   - Treatment response monitoring

### Medium-Term Goals (3-5 years)

1. **Clinical Implementation**
   - Real-time patient profiling platforms
   - Therapy selection algorithms
   - Response prediction models

2. **Drug Discovery Platform**
   - Automated target identification
   - Drug repurposing applications
   - Combination therapy design

3. **Personalized Medicine**
   - Individual patient CD4 profiling
   - Customized immunotherapy protocols
   - Adverse event risk prediction

### Long-Term Vision (5-10 years)

1. **Systems Immunology Platform**
   - Complete immune system modeling
   - Multi-organ immune interactions
   - Aging and disease progression models

2. **AI-Driven Drug Discovery**
   - Automated therapeutic design
   - Virtual clinical trials
   - Precision medicine optimization

3. **Global Health Applications**
   - Vaccine design for emerging pathogens
   - Population-specific immunotherapy
   - Health disparities reduction

---

## Contributing and Support

### Development Guidelines

**Code Standards**
- **Python Style**: PEP 8 compliance
- **Documentation**: Comprehensive docstrings
- **Testing**: Unit tests for all functions
- **Version Control**: Git with semantic versioning

**Data Standards**
- **File Formats**: AnnData for single-cell, NetworkX for graphs
- **Metadata**: Complete provenance tracking
- **Quality Control**: Automated validation pipelines
- **Reproducibility**: Fixed random seeds, environment specs

### Community Resources

**Documentation**
- **API Reference**: Complete function documentation
- **Tutorials**: Step-by-step guides
- **Examples**: Real-world use cases
- **FAQ**: Common questions and solutions

**Support Channels**
- **GitHub Issues**: Bug reports and feature requests
- **Discussions**: Community Q&A
- **Slack/Discord**: Real-time chat support
- **Workshops**: Regular training sessions

### Citation and Acknowledgments

**Primary Citation**
```bibtex
@article{cd4_pinnacle_2024,
  title={CD4-PINNACLE: Context-Aware Protein Embeddings for Precision Immunotherapy},
  author={[Authors]},
  journal={[Journal]},
  year={2024},
  doi={[DOI]}
}
```

**Acknowledgments**
- Original PINNACLE developers (Zitnik Lab, Harvard)
- CD4 T cell atlas contributors
- Computational infrastructure providers
- Clinical collaborators and data contributors

---

## Contact and Collaboration

**Principal Investigators**
- [Primary Contact]: [Email]
- [Collaborating Institutions]

**Technical Support**
- **Email**: cd4-pinnacle-support@[domain]
- **GitHub**: [Repository URL]
- **Website**: [Project Website]

**Collaboration Opportunities**
- **Academic Partnerships**: Joint research projects
- **Industry Collaborations**: Commercial applications
- **Clinical Studies**: Validation and implementation
- **Open Source Contributions**: Community development

---

*This documentation is maintained by the CD4-PINNACLE development team and updated regularly. For the latest version, please visit our GitHub repository.*