#!/usr/bin/env python3
"""
Convert CD4 PPI networks to PINNACLE training format
"""

import pandas as pd
import networkx as nx
import os
import sys

# Add parent directory to path
sys.path.insert(0, '..')
from data_config import OUTPUT_DIR

def load_global_ppi():
    """Load global PPI network"""
    ppi_file = "../data/networks/global_ppi_edgelist.txt"
    print(f"Loading global PPI from: {ppi_file}")
    
    G = nx.Graph()
    with open(ppi_file, 'r') as f:
        for line in f:
            if line.strip():
                parts = line.strip().split('\t')
                if len(parts) >= 2:
                    G.add_edge(parts[0], parts[1])
    
    print(f"Global PPI: {G.number_of_nodes()} nodes, {G.number_of_edges()} edges")
    return G

def convert_cd4_networks_to_edgelists():
    """Convert CD4 subtype networks to individual edgelist files"""
    
    # Load CD4 networks
    cd4_file = "../data/networks/cd4_celltype_ppi_pval1.0_genes4000.csv"
    print(f"Loading CD4 networks from: {cd4_file}")
    
    # Read the file and handle escaped newlines
    with open(cd4_file, 'r') as f:
        content = f.read()
    
    # Replace escaped newlines with actual newlines
    content = content.replace('\\n', '\n')
    
    # Write corrected content to a temporary file
    import tempfile
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as tmp:
        tmp.write(content)
        tmp_file = tmp.name
    
    try:
        df = pd.read_csv(tmp_file)
    finally:
        os.unlink(tmp_file)
    
    print(f"Found {len(df)} CD4 subtypes")
    
    # Load global PPI for edge information
    global_ppi = load_global_ppi()
    
    # Create output directory
    output_dir = "../data/networks/ppi_edgelists"
    os.makedirs(output_dir, exist_ok=True)
    
    networks_created = 0
    total_edges = 0
    
    for idx, row in df.iterrows():
        subtype_name = row['subtype_name']
        proteins = row['proteins'].split(',')
        
        # Clean protein names
        proteins = [p.strip() for p in proteins if p.strip()]
        
        print(f"\nProcessing {subtype_name}: {len(proteins)} proteins")
        
        # Extract subnetwork from global PPI
        subnetwork = global_ppi.subgraph(proteins).copy()
        
        if subnetwork.number_of_edges() == 0:
            print(f"  Warning: No edges found for {subtype_name}")
            continue
        
        # Save as edgelist file
        # Use safe filename (replace spaces and special characters)
        safe_name = subtype_name.replace(' ', '_').replace('/', '_').replace('-', '_')
        safe_name = ''.join(c for c in safe_name if c.isalnum() or c in '_')
        
        output_file = os.path.join(output_dir, f"{safe_name}.txt")
        
        with open(output_file, 'w') as f:
            for edge in subnetwork.edges():
                f.write(f"{edge[0]}\t{edge[1]}\n")
        
        networks_created += 1
        total_edges += subnetwork.number_of_edges()
        
        print(f"  Saved: {output_file}")
        print(f"  Edges: {subnetwork.number_of_edges()}")
    
    print(f"\n=== Summary ===")
    print(f"Networks created: {networks_created}")
    print(f"Total edges: {total_edges}")
    if networks_created > 0:
        print(f"Average edges per network: {total_edges/networks_created:.1f}")
    else:
        print("No networks were created successfully")
    
    return networks_created

def create_cd4_subtype_list():
    """Create list of valid CD4 subtypes for training"""
    
    cd4_file = "../data/networks/cd4_celltype_ppi_pval1.0_genes4000.csv"
    
    # Read and fix escaped newlines
    with open(cd4_file, 'r') as f:
        content = f.read().replace('\\n', '\n')
    
    import tempfile
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as tmp:
        tmp.write(content)
        tmp_file = tmp.name
    
    try:
        df = pd.read_csv(tmp_file)
    finally:
        os.unlink(tmp_file)
    
    # Create valid subtypes list
    subtypes_data = []
    for idx, row in df.iterrows():
        subtype_name = row['subtype_name']
        safe_name = subtype_name.replace(' ', '_').replace('/', '_').replace('-', '_')
        safe_name = ''.join(c for c in safe_name if c.isalnum() or c in '_')
        
        subtypes_data.append({
            'index': idx,
            'subtype': subtype_name,
            'safe_name': safe_name,
            'protein_count': len(row['proteins'].split(','))
        })
    
    subtypes_df = pd.DataFrame(subtypes_data)
    
    # Save subtypes list
    output_file = "../data/networks/cd4_valid_subtypes.csv"
    subtypes_df.to_csv(output_file, index=False)
    
    print(f"Saved CD4 subtypes list: {output_file}")
    print(f"Subtypes: {len(subtypes_df)}")
    
    return subtypes_df

def create_cd4_metagraph():
    """Create simple metagraph for CD4 subtypes"""
    
    # For now, create a simple linear metagraph connecting all CD4 subtypes
    # In a more sophisticated version, this could be based on biological relationships
    
    cd4_file = "../data/networks/cd4_celltype_ppi_pval1.0_genes4000.csv"
    
    # Read and fix escaped newlines
    with open(cd4_file, 'r') as f:
        content = f.read().replace('\\n', '\n')
    
    import tempfile
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as tmp:
        tmp.write(content)
        tmp_file = tmp.name
    
    try:
        df = pd.read_csv(tmp_file)
    finally:
        os.unlink(tmp_file)
    
    subtypes = []
    for idx, row in df.iterrows():
        subtype_name = row['subtype_name']
        safe_name = subtype_name.replace(' ', '_').replace('/', '_').replace('-', '_')
        safe_name = ''.join(c for c in safe_name if c.isalnum() or c in '_')
        subtypes.append(safe_name)
    
    # Create metagraph edges (simple chain for now)
    mg_edges = []
    for i in range(len(subtypes)-1):
        mg_edges.append((subtypes[i], subtypes[i+1]))
    
    # Add some additional connections for better connectivity
    # Connect functional groups
    treg_subtypes = [s for s in subtypes if 'Treg' in s]
    naive_subtypes = [s for s in subtypes if 'Naive' in s]
    memory_subtypes = [s for s in subtypes if 'Memory' in s or 'Mem' in s]
    
    # Connect within functional groups
    for group in [treg_subtypes, naive_subtypes, memory_subtypes]:
        for i in range(len(group)-1):
            mg_edges.append((group[i], group[i+1]))
    
    # Save metagraph
    output_file = "../data/networks/cd4_mg_edgelist.txt"
    with open(output_file, 'w') as f:
        for edge in mg_edges:
            f.write(f"{edge[0]}\t{edge[1]}\n")
    
    print(f"Created CD4 metagraph: {output_file}")
    print(f"Metagraph edges: {len(mg_edges)}")
    
    return mg_edges

def main():
    """Main conversion function"""
    print("="*60)
    print("Converting CD4 Networks to PINNACLE Format")
    print("="*60)
    
    # Convert networks to edgelists
    networks_created = convert_cd4_networks_to_edgelists()
    
    # Create subtypes list
    subtypes_df = create_cd4_subtype_list()
    
    # Create metagraph
    mg_edges = create_cd4_metagraph()
    
    print(f"\n✓ Conversion completed!")
    print(f"✓ {networks_created} CD4 subtype networks ready for training")
    print(f"✓ Files created in: ../data/networks/")

if __name__ == "__main__":
    main()