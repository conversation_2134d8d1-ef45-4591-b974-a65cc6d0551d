Starting CD4 preprocessing pipeline at Fri Jul  4 11:22:26 CST 2025
Project directory: /mnt/f/文献汇报/21_PINNACLE/T细胞图谱/CD4_PINNACLE_project
Log file: /mnt/f/文献汇报/21_PINNACLE/T细胞图谱/CD4_PINNACLE_project/logs/cd4_preprocessing_20250704_112226.log

✓ CD4 data file found: /mnt/f/文献汇报/21_PINNACLE/T细胞图谱/CD4_PINNACLE_project/data/raw/CD4.h5ad

>>> STEP 1: CD4 Subtype Gene Ranking <<<
============================================
Activating conda environment: scRNA_env
✓ Activated environment: scRNA_env
Running gene ranking analysis...
run_cd4_preprocessing.sh: line 86: 547911 Killed                  python data_prep/cd4_constructPPI.py -rank True -rank_pval_filename "${NETWORKS_DIR}/cd4_ranked_genes" -annotation "annotation_correction_low" -min_cells 100 -method "wilcoxon" -subsample False
