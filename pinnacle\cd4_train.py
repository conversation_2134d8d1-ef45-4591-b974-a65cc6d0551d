#!/usr/bin/env python3
"""
CD4-PINNACLE Training Script
Specialized training pipeline for CD4 T cell subtype-specific protein embeddings

This script adapts the original PINNACLE training for CD4 T cell data,
optimizing hyperparameters and loss functions for T cell biology.
"""

# General imports
import numpy as np
import random
import argparse
import os
import copy
import time
from datetime import datetime

# PyTorch imports
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_geometric.utils.convert import to_networkx, to_scipy_sparse_matrix
from torch_geometric.data import Data
from torch_geometric.utils import negative_sampling

# PINNACLE modules
from center_loss import CenterLoss
from generate_input import read_data, get_metapaths, get_centerloss_labels
import model as mdl
import utils
import minibatch_utils as mb_utils
from parse_args import get_args, get_hparams

# CD4-specific modules
import sys
sys.path.insert(0, '..')
from data_config import *

# Experiment tracking
import wandb

# Set random seeds for reproducibility
def set_seeds(seed=42):
    """Set random seeds for reproducible results"""
    print(f"Setting random seed: {seed}")
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    random.seed(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False

def get_cd4_args():
    """Get CD4-specific command line arguments"""
    parser = argparse.ArgumentParser(description="CD4-PINNACLE Training")
    
    # Input files (CD4-specific paths)
    parser.add_argument("--G_f", type=str, 
                       default="../data/networks/global_ppi_edgelist.txt",
                       help="Global reference PPI network")
    parser.add_argument("--ppi_dir", type=str,
                       default="../data/networks/ppi_edgelists/",
                       help="Directory with CD4 subtype-specific PPI networks")
    parser.add_argument("--mg_f", type=str,
                       default="../data/networks/mg_edgelist.txt",
                       help="CD4 metagraph file")
    
    # CD4-specific parameters
    parser.add_argument("--cd4_subtypes_file", type=str,
                       default="../data/networks/cd4_valid_subtypes.csv",
                       help="File with valid CD4 subtypes")
    parser.add_argument("--annotation_col", type=str,
                       default="annotation_correction_low",
                       help="CD4 subtype annotation column")
    
    # Training parameters
    parser.add_argument("--epochs", type=int, default=300,
                       help="Number of training epochs")
    parser.add_argument("--patience", type=int, default=30,
                       help="Early stopping patience")
    parser.add_argument("--resume_run", type=str, default="",
                       help="Resume from checkpoint")
    
    # Model hyperparameters (optimized for CD4 T cells)
    parser.add_argument("--feat_mat", type=int, default=2048,
                       help="Random feature matrix size")
    parser.add_argument("--output", type=int, default=8,
                       help="Output embedding dimension")
    parser.add_argument("--hidden", type=int, default=16,
                       help="Hidden layer size")
    parser.add_argument("--n_heads", type=int, default=8,
                       help="Number of attention heads")
    parser.add_argument("--pc_att_channels", type=int, default=8,
                       help="Protein-cell attention channels")
    
    # Loss function weights (tuned for CD4 biology)
    parser.add_argument("--theta", type=float, default=0.15,
                       help="Weight for PPI loss (increased for T cell networks)")
    parser.add_argument("--lmbda", type=float, default=0.02,
                       help="Weight for center loss (increased for subtype separation)")
    parser.add_argument("--alpha", type=float, default=0.1,
                       help="Weight for subtype classification loss")
    
    # Optimization parameters
    parser.add_argument("--lr", type=float, default=0.001,
                       help="Learning rate")
    parser.add_argument("--wd", type=float, default=5e-4,
                       help="Weight decay")
    parser.add_argument("--dropout", type=float, default=0.3,
                       help="Dropout rate (increased for CD4 complexity)")
    parser.add_argument("--lr_cent", type=float, default=0.01,
                       help="Learning rate for center loss")
    parser.add_argument("--batch_size", type=int, default=64,
                       help="Batch size")
    parser.add_argument("--gradclip", type=float, default=1.0,
                       help="Gradient clipping value")
    
    # Data loading parameters
    parser.add_argument("--loader", type=str, default="graphsaint",
                       choices=["neighbor", "graphsaint"],
                       help="Minibatch loader type")
    parser.add_argument("--norm", type=str, default=None,
                       help="Normalization type")
    
    # Output parameters
    parser.add_argument("--save_prefix", type=str,
                       default="../data/pinnacle_embeds/cd4_pinnacle",
                       help="Prefix for saved files")
    parser.add_argument("--plot", type=bool, default=True,
                       help="Generate and save plots")
    parser.add_argument("--wandb_project", type=str,
                       default="cd4-pinnacle",
                       help="Weights & Biases project name")
    
    return parser.parse_args()

def get_cd4_hparams(args):
    """Get CD4-optimized hyperparameters"""
    hparams = {
        # Model architecture
        'pc_att_channels': args.pc_att_channels,
        'feat_mat': args.feat_mat,
        'output': args.output,
        'hidden': args.hidden,
        'n_heads': args.n_heads,
        'dropout': args.dropout,
        
        # Optimization
        'lr': args.lr,
        'wd': args.wd,
        'gradclip': args.gradclip,
        'lr_cent': args.lr_cent,
        
        # Loss weights (CD4-optimized)
        'theta': args.theta,        # PPI loss weight
        'lambda': args.lmbda,       # Center loss weight  
        'alpha': args.alpha,        # Classification loss weight
        
        # Training
        'loss_type': "BCE",
        'plot': args.plot,
        'batch_size': args.batch_size,
        
        # CD4-specific
        'annotation_col': args.annotation_col,
        'target_cell_type': 'CD4_T_cells'
    }
    
    print("\\n=== CD4-PINNACLE Hyperparameters ===")
    for key, value in hparams.items():
        print(f"{key}: {value}")
    print("=" * 40)
    
    return hparams

def load_cd4_subtypes(subtypes_file):
    """Load valid CD4 subtypes for training"""
    if os.path.exists(subtypes_file):
        import pandas as pd
        subtypes_df = pd.read_csv(subtypes_file)
        subtypes = subtypes_df['subtype'].tolist()
        print(f"Loaded {len(subtypes)} valid CD4 subtypes")
        return subtypes
    else:
        print(f"Warning: CD4 subtypes file not found: {subtypes_file}")
        return None

def create_cd4_data_loaders(args, hparams):
    """Create data loaders for CD4 training"""
    print("\\n=== Loading CD4 Data ===")
    
    # Load CD4 subtypes
    cd4_subtypes = load_cd4_subtypes(args.cd4_subtypes_file)
    
    # Read and process data
    graph_data = read_data(
        global_ppi_f=args.G_f,
        ppi_dir=args.ppi_dir,
        mg_f=args.mg_f,
        celltype_filter=cd4_subtypes,  # Filter to CD4 subtypes only
        min_nodes=50  # Minimum nodes per network
    )
    
    # Get metapaths and data structures
    metapaths_data = get_metapaths(graph_data)
    
    # Create minibatch loaders
    if args.loader == "graphsaint":
        train_loaders = mb_utils.create_graphsaint_loaders(
            graph_data, 
            batch_size=args.batch_size,
            num_steps=3,  # Optimized for CD4 networks
            sample_coverage=0.8
        )
    else:
        train_loaders = mb_utils.create_neighbor_loaders(
            graph_data,
            batch_size=args.batch_size,
            num_neighbors=[15, 10]  # Neighbor sampling sizes
        )
    
    print(f"✓ Created data loaders for {len(graph_data['ppi_data'])} CD4 subtypes")
    
    return graph_data, metapaths_data, train_loaders

def create_cd4_model(graph_data, hparams, device):
    """Create CD4-PINNACLE model"""
    print("\\n=== Creating CD4-PINNACLE Model ===")
    
    # Model parameters
    nfeat = hparams['feat_mat']
    hidden = hparams['hidden']
    output = hparams['output']
    n_heads = hparams['n_heads']
    pc_att_channels = hparams['pc_att_channels']
    dropout = hparams['dropout']
    
    # Get number of relations
    num_ppi_relations = len(graph_data['ppi_data'])
    num_mg_relations = len(graph_data['mg_edge_types'])
    
    print(f"Model configuration:")
    print(f"  Input features: {nfeat}")
    print(f"  Hidden size: {hidden}")
    print(f"  Output size: {output}")
    print(f"  Attention heads: {n_heads}")
    print(f"  PPI relations: {num_ppi_relations}")
    print(f"  MG relations: {num_mg_relations}")
    
    # Create model
    model = mdl.Pinnacle(
        nfeat=nfeat,
        hidden=hidden,
        output=output,
        num_ppi_relations=num_ppi_relations,
        num_mg_relations=num_mg_relations,
        ppi_data=graph_data['ppi_data'],
        n_heads=n_heads,
        pc_att_channels=pc_att_channels,
        dropout=dropout
    ).to(device)
    
    # Center loss for CD4 subtype separation
    center_loss = CenterLoss(
        num_classes=num_ppi_relations,
        feat_dim=output * n_heads,
        use_gpu=device.type == 'cuda'
    )
    
    print(f"✓ Model created with {sum(p.numel() for p in model.parameters())} parameters")
    
    return model, center_loss

def cd4_training_step(model, center_loss, optimizer, optimizer_centloss,
                     batch_data, hparams, device):
    """Single training step for CD4-PINNACLE"""
    model.train()
    
    # Unpack batch data
    ppi_x, mg_x, ppi_metapaths, mg_metapaths = batch_data['features']
    ppi_edge_index, mg_edge_index = batch_data['edge_indices']
    ppi_y, mg_y = batch_data['targets']
    tissue_neighbors = batch_data['tissue_neighbors']
    
    # Forward pass
    ppi_embeddings, mg_embeddings = model(
        ppi_x, mg_x, ppi_metapaths, mg_metapaths,
        ppi_edge_index, mg_edge_index, tissue_neighbors
    )
    
    # Calculate losses
    from loss import calc_link_pred_loss, calc_center_loss
    
    # 1. Link prediction loss
    ppi_pred = model.predict_links(ppi_embeddings, ppi_edge_index)
    mg_pred = model.predict_links(mg_embeddings, mg_edge_index)
    
    ppi_loss, mg_loss = calc_link_pred_loss(mg_pred, mg_y, ppi_pred, ppi_y)
    link_loss = ppi_loss + mg_loss
    
    # 2. Center loss for CD4 subtype separation
    center_labels = get_centerloss_labels(ppi_embeddings, batch_data['subtype_labels'])
    center_loss_val = calc_center_loss(
        center_loss, ppi_embeddings, center_labels,
        mask=batch_data['protein_mask']
    )
    
    # 3. CD4 subtype classification loss (optional)
    if 'subtype_classification' in batch_data:
        classification_loss = F.cross_entropy(
            batch_data['subtype_logits'],
            batch_data['subtype_labels']
        )
    else:
        classification_loss = 0
    
    # Total loss with CD4-specific weights
    total_loss = (
        hparams['theta'] * link_loss +
        hparams['lambda'] * center_loss_val +
        hparams.get('alpha', 0.1) * classification_loss
    )
    
    # Backward pass
    optimizer.zero_grad()
    optimizer_centloss.zero_grad()
    
    total_loss.backward()
    
    # Gradient clipping
    torch.nn.utils.clip_grad_norm_(model.parameters(), hparams['gradclip'])
    
    optimizer.step()
    optimizer_centloss.step()
    
    # Return loss components
    return {
        'total_loss': total_loss.item(),
        'link_loss': link_loss.item(),
        'center_loss': center_loss_val.item(),
        'classification_loss': classification_loss.item() if isinstance(classification_loss, torch.Tensor) else classification_loss
    }

def train_cd4_pinnacle(model, center_loss, graph_data, train_loaders, hparams, args):
    """Main training loop for CD4-PINNACLE"""
    print("\\n=== Starting CD4-PINNACLE Training ===")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Training device: {device}")
    
    # Optimizers
    optimizer = torch.optim.Adam(
        model.parameters(),
        lr=hparams['lr'],
        weight_decay=hparams['wd']
    )
    
    optimizer_centloss = torch.optim.SGD(
        center_loss.parameters(),
        lr=hparams['lr_cent']
    )
    
    # Learning rate scheduler
    scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
        optimizer, T_max=args.epochs, eta_min=hparams['lr'] * 0.01
    )
    
    # Training tracking
    best_loss = float('inf')
    patience_counter = 0
    train_losses = []
    
    print(f"Training for {args.epochs} epochs with patience {args.patience}")
    
    for epoch in range(args.epochs):
        epoch_start_time = time.time()
        epoch_losses = []
        
        # Training step
        for batch_idx, batch_data in enumerate(train_loaders):
            losses = cd4_training_step(
                model, center_loss, optimizer, optimizer_centloss,
                batch_data, hparams, device
            )
            epoch_losses.append(losses)
            
            # Log batch progress
            if batch_idx % 10 == 0:
                print(f"  Batch {batch_idx}: Loss = {losses['total_loss']:.4f}")
        
        # Calculate epoch averages
        avg_losses = {
            key: np.mean([loss[key] for loss in epoch_losses])
            for key in epoch_losses[0].keys()
        }
        
        train_losses.append(avg_losses['total_loss'])
        
        # Update learning rate
        scheduler.step()
        current_lr = scheduler.get_last_lr()[0]
        
        # Log epoch progress
        epoch_time = time.time() - epoch_start_time
        print(f"\\nEpoch {epoch+1}/{args.epochs} ({epoch_time:.1f}s):")
        print(f"  Total Loss: {avg_losses['total_loss']:.4f}")
        print(f"  Link Loss: {avg_losses['link_loss']:.4f}")
        print(f"  Center Loss: {avg_losses['center_loss']:.4f}")
        print(f"  Classification Loss: {avg_losses['classification_loss']:.4f}")
        print(f"  Learning Rate: {current_lr:.6f}")
        
        # Weights & Biases logging
        if 'wandb' in globals():
            wandb.log({
                'epoch': epoch + 1,
                'total_loss': avg_losses['total_loss'],
                'link_loss': avg_losses['link_loss'],
                'center_loss': avg_losses['center_loss'],
                'classification_loss': avg_losses['classification_loss'],
                'learning_rate': current_lr,
                'epoch_time': epoch_time
            })
        
        # Early stopping check
        if avg_losses['total_loss'] < best_loss:
            best_loss = avg_losses['total_loss']
            patience_counter = 0
            
            # Save best model
            best_model_path = f"{args.save_prefix}_best_model.pth"
            torch.save({
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'center_loss_state_dict': center_loss.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'best_loss': best_loss,
                'hparams': hparams
            }, best_model_path)
            
            print(f"  ✓ New best model saved (loss: {best_loss:.4f})")
        else:
            patience_counter += 1
        
        # Early stopping
        if patience_counter >= args.patience:
            print(f"\\nEarly stopping triggered after {epoch+1} epochs")
            break
        
        print("-" * 60)
    
    print(f"\\n✓ Training completed!")
    print(f"✓ Best loss: {best_loss:.4f}")
    print(f"✓ Total epochs: {epoch+1}")
    
    return model, train_losses

def save_cd4_embeddings(model, graph_data, args, hparams):
    """Save CD4-specific protein embeddings"""
    print("\\n=== Saving CD4 Embeddings ===")
    
    model.eval()
    device = next(model.parameters()).device
    
    # Generate embeddings for all CD4 subtypes
    cd4_embeddings = {}
    
    with torch.no_grad():
        for subtype, ppi_data in graph_data['ppi_data'].items():
            # Create input features
            x = torch.randn(len(ppi_data['proteins']), hparams['feat_mat']).to(device)
            
            # Forward pass to get embeddings
            subtype_embedding = model.get_protein_embeddings(x, subtype)
            cd4_embeddings[subtype] = subtype_embedding.cpu()
            
            print(f"  {subtype}: {subtype_embedding.shape}")
    
    # Save embeddings
    embeddings_path = f"{args.save_prefix}_protein_embed.pth"
    torch.save(cd4_embeddings, embeddings_path)
    
    # Save protein mappings
    protein_mappings = {}
    for subtype, ppi_data in graph_data['ppi_data'].items():
        protein_mappings[subtype] = ppi_data['proteins']
    
    mappings_path = f"{args.save_prefix}_protein_mappings.pth"
    torch.save(protein_mappings, mappings_path)
    
    print(f"✓ CD4 embeddings saved: {embeddings_path}")
    print(f"✓ Protein mappings saved: {mappings_path}")
    
    return cd4_embeddings

def main():
    """Main CD4-PINNACLE training function"""
    print("="*70)
    print("CD4-PINNACLE: Context-Aware Protein Embeddings for CD4 T Cells")
    print("="*70)
    
    # Set random seeds
    set_seeds(42)
    
    # Parse arguments
    args = get_cd4_args()
    hparams = get_cd4_hparams(args)
    
    # Initialize Weights & Biases
    if args.wandb_project:
        wandb.init(
            project=args.wandb_project,
            config=hparams,
            name=f"cd4_pinnacle_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        )
    
    # Create output directory
    os.makedirs(os.path.dirname(args.save_prefix), exist_ok=True)
    
    # Load data
    graph_data, metapaths_data, train_loaders = create_cd4_data_loaders(args, hparams)
    
    # Create model
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model, center_loss = create_cd4_model(graph_data, hparams, device)
    
    # Train model
    trained_model, train_losses = train_cd4_pinnacle(
        model, center_loss, graph_data, train_loaders, hparams, args
    )
    
    # Save embeddings
    cd4_embeddings = save_cd4_embeddings(trained_model, graph_data, args, hparams)
    
    # Generate visualizations if requested
    if args.plot:
        print("\\n=== Generating Visualizations ===")
        from cd4_visualization import create_training_plots
        create_training_plots(train_losses, cd4_embeddings, args.save_prefix)
    
    # Save final results
    results = {
        'model_state_dict': trained_model.state_dict(),
        'train_losses': train_losses,
        'hparams': hparams,
        'cd4_subtypes': list(graph_data['ppi_data'].keys()),
        'training_args': vars(args)
    }
    
    results_path = f"{args.save_prefix}_training_results.pth"
    torch.save(results, results_path)
    
    print(f"\\n✓ Training results saved: {results_path}")
    print(f"\\n{'='*70}")
    print("CD4-PINNACLE training completed successfully!")
    print(f"{'='*70}")
    
    return trained_model, cd4_embeddings

if __name__ == "__main__":
    main()