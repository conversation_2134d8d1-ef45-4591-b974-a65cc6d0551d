#!/bin/bash

# CD4-PINNACLE Data Preprocessing Pipeline
# Complete automation script for CD4 T cell subtype-specific network construction

set -e  # Exit on any error

echo "=================================================================="
echo "CD4-PINNACLE Data Preprocessing Pipeline"
echo "=================================================================="

# Configuration
PROJECT_DIR="/mnt/f/文献汇报/21_PINNACLE/T细胞图谱/CD4_PINNACLE_project"
DATA_DIR="${PROJECT_DIR}/data"
NETWORKS_DIR="${DATA_DIR}/networks"
CELLPHONEDB_DIR="${DATA_DIR}/cellphonedb"
LOGS_DIR="${PROJECT_DIR}/logs"

# Create directories
mkdir -p "${NETWORKS_DIR}"
mkdir -p "${CELLPHONEDB_DIR}"
mkdir -p "${LOGS_DIR}"

# Log file
LOG_FILE="${LOGS_DIR}/cd4_preprocessing_$(date +%Y%m%d_%H%M%S).log"
exec > >(tee -a "${LOG_FILE}")
exec 2>&1

echo "Starting CD4 preprocessing pipeline at $(date)"
echo "Project directory: ${PROJECT_DIR}"
echo "Log file: ${LOG_FILE}"
echo ""

# Check if CD4 data exists
if [ ! -f "${DATA_DIR}/raw/CD4.h5ad" ]; then
    echo "Error: CD4.h5ad not found in ${DATA_DIR}/raw/"
    exit 1
fi

echo "✓ CD4 data file found: ${DATA_DIR}/raw/CD4.h5ad"

# Function to check command success
check_success() {
    if [ $? -eq 0 ]; then
        echo "✓ $1 completed successfully"
    else
        echo "✗ $1 failed"
        exit 1
    fi
}

# Function to activate conda environment
activate_env() {
    local env_name=$1
    echo "Activating conda environment: ${env_name}"
    
    # Initialize conda
    eval "$(conda shell.bash hook)"
    
    if conda env list | grep -q "^${env_name} "; then
        conda activate "${env_name}"
        echo "✓ Activated environment: ${env_name}"
    else
        echo "Warning: Environment ${env_name} not found. Please create it first."
        echo "For scRNA_env: conda env create -f data_prep/scRNA_env.yml"
        echo "For pinnacle: conda env create -f environment.yml"
        exit 1
    fi
}

# Step 1: Gene Ranking
echo ""
echo ">>> STEP 1: CD4 Subtype Gene Ranking <<<"
echo "============================================"

cd "${PROJECT_DIR}"
activate_env "scRNA_env"

echo "Running gene ranking analysis..."
python data_prep/cd4_constructPPI.py \
    -rank True \
    -rank_pval_filename "${NETWORKS_DIR}/cd4_ranked_genes" \
    -annotation "annotation_correction_low" \
    -min_cells 100 \
    -method "wilcoxon" \
    -subsample False

check_success "Gene ranking"

# Step 2: PPI Network Extraction
echo ""
echo ">>> STEP 2: CD4 Subtype-Specific PPI Network Construction <<<"
echo "============================================================="

echo "Extracting CD4 subtype-specific PPI networks..."
python data_prep/cd4_constructPPI.py \
    -rank False \
    -rank_pval_filename "${NETWORKS_DIR}/cd4_ranked_genes.csv" \
    -celltype_ppi_filename "${NETWORKS_DIR}/cd4_celltype_ppi" \
    -max_pval 1.0 \
    -max_num_genes 4000 \
    -lcc True

check_success "PPI network extraction"

# Step 3: Evaluate PPI Networks
echo ""
echo ">>> STEP 3: PPI Network Evaluation <<<"
echo "====================================="

if [ -f "data_prep/1.evaluatePPI.py" ]; then
    echo "Evaluating CD4 subtype PPI networks..."
    python data_prep/1.evaluatePPI.py \
        -celltype_ppi "${NETWORKS_DIR}/cd4_celltype_ppi_pval1.0_genes4000.csv" \
        -max_pval 1.0 \
        -max_num_genes 4000
    
    check_success "PPI network evaluation"
else
    echo "Note: PPI evaluation script not found, skipping evaluation step"
fi

# Step 4: Prepare CellPhoneDB Input
echo ""
echo ">>> STEP 4: CellPhoneDB Preparation <<<"
echo "====================================="

if [ -f "data_prep/2.prepCellPhoneDB.py" ]; then
    echo "Preparing CellPhoneDB input files..."
    python data_prep/2.prepCellPhoneDB.py \
        -data "${DATA_DIR}/raw/CD4.h5ad" \
        -output "${CELLPHONEDB_DIR}/" \
        -annotation "annotation_correction_low" \
        -get_counts False
    
    check_success "CellPhoneDB preparation"
else
    echo "Note: CellPhoneDB preparation script not found, skipping"
fi

# Step 5: Run CellPhoneDB (if environment available)
echo ""
echo ">>> STEP 5: CellPhoneDB Analysis <<<"
echo "==================================="

# Check if CellPhoneDB environment exists
if conda env list | grep -q "cpdb"; then
    echo "Running CellPhoneDB analysis..."
    activate_env "cpdb"
    
    cd "${CELLPHONEDB_DIR}"
    
    if [ -f "meta_CellPhoneDB.txt" ] && [ -f "../raw/CD4.h5ad" ]; then
        cellphonedb method statistical_analysis \
            meta_CellPhoneDB.txt \
            ../raw/CD4.h5ad \
            --counts-data hgnc_symbol \
            --output-path "${CELLPHONEDB_DIR}/results" \
            --threads 4
        
        check_success "CellPhoneDB analysis"
    else
        echo "Warning: CellPhoneDB input files not found"
    fi
    
    cd "${PROJECT_DIR}"
else
    echo "Note: CellPhoneDB environment (cpdb) not found"
    echo "To create: conda create -n cpdb python=3.7 && conda activate cpdb && pip install cellphonedb"
fi

# Step 6: Construct Cell-Cell Interaction Network
echo ""
echo ">>> STEP 6: Cell-Cell Interaction Network <<<"
echo "============================================="

activate_env "scRNA_env"

if [ -f "data_prep/4.constructCCI.py" ] && [ -f "${CELLPHONEDB_DIR}/results/pvalues.txt" ]; then
    echo "Constructing cell-cell interaction networks..."
    python data_prep/4.constructCCI.py \
        -cpdb_output "${CELLPHONEDB_DIR}/results/pvalues.txt" \
        -cci_edgelist "${NETWORKS_DIR}/cd4_cci_edgelist.txt"
    
    check_success "Cell-cell interaction network construction"
else
    echo "Note: Skipping CCI construction (missing prerequisites)"
fi

# Step 7: Construct Metagraph
echo ""
echo ">>> STEP 7: Metagraph Construction <<<"
echo "===================================="

if [ -f "data_prep/5.constructMG.py" ]; then
    echo "Constructing CD4 metagraph..."
    python data_prep/5.constructMG.py \
        -celltype_ppi "${NETWORKS_DIR}/cd4_celltype_ppi_pval1.0_genes4000.csv" \
        -cci_edgelist "${NETWORKS_DIR}/cd4_cci_edgelist.txt" \
        -mg_edgelist "${NETWORKS_DIR}/mg_edgelist.txt"
    
    check_success "Metagraph construction"
else
    echo "Note: Metagraph construction script not found"
fi

# Step 8: Generate Summary Report
echo ""
echo ">>> STEP 8: Summary Report Generation <<<"
echo "========================================"

echo "Generating preprocessing summary..."

SUMMARY_FILE="${PROJECT_DIR}/cd4_preprocessing_summary.md"

cat > "${SUMMARY_FILE}" << EOF
# CD4-PINNACLE Preprocessing Summary

**Generated:** $(date)
**Pipeline:** CD4 T Cell Subtype-Specific Network Construction

## Data Processing Results

### Input Data
- **Source:** ${DATA_DIR}/raw/CD4.h5ad
- **Annotation Column:** annotation_correction_low
- **Minimum Cells per Subtype:** 100

### Generated Files

#### Gene Rankings
- **File:** ${NETWORKS_DIR}/cd4_ranked_genes.csv
- **Method:** Wilcoxon rank-sum test
- **Description:** Ranked genes for each CD4 subtype

#### PPI Networks
- **File:** ${NETWORKS_DIR}/cd4_celltype_ppi_pval1.0_genes4000.csv
- **Max P-value:** 1.0
- **Max Genes:** 4000
- **LCC:** True
- **Description:** CD4 subtype-specific protein interaction networks

EOF

# Add file existence checks to summary
echo "" >> "${SUMMARY_FILE}"
echo "### File Verification" >> "${SUMMARY_FILE}"
echo "" >> "${SUMMARY_FILE}"

files_to_check=(
    "${NETWORKS_DIR}/cd4_ranked_genes.csv"
    "${NETWORKS_DIR}/cd4_ranked_genes.h5ad"
    "${NETWORKS_DIR}/cd4_celltype_ppi_pval1.0_genes4000.csv"
    "${NETWORKS_DIR}/global_ppi_edgelist.txt"
    "${NETWORKS_DIR}/cd4_cci_edgelist.txt"
    "${NETWORKS_DIR}/mg_edgelist.txt"
)

for file in "${files_to_check[@]}"; do
    if [ -f "$file" ]; then
        size=$(du -h "$file" | cut -f1)
        echo "- ✓ $(basename "$file"): $size" >> "${SUMMARY_FILE}"
    else
        echo "- ✗ $(basename "$file"): Not found" >> "${SUMMARY_FILE}"
    fi
done

echo "" >> "${SUMMARY_FILE}"
echo "### Next Steps" >> "${SUMMARY_FILE}"
echo "" >> "${SUMMARY_FILE}"
echo "1. **Train CD4-PINNACLE Model:**" >> "${SUMMARY_FILE}"
echo "   \`\`\`bash" >> "${SUMMARY_FILE}"
echo "   cd pinnacle" >> "${SUMMARY_FILE}"
echo "   bash run_pinnacle.sh" >> "${SUMMARY_FILE}"
echo "   \`\`\`" >> "${SUMMARY_FILE}"
echo "" >> "${SUMMARY_FILE}"
echo "2. **Evaluate Results:**" >> "${SUMMARY_FILE}"
echo "   \`\`\`bash" >> "${SUMMARY_FILE}"
echo "   cd evaluate" >> "${SUMMARY_FILE}"
echo "   python visualize_representations.py" >> "${SUMMARY_FILE}"
echo "   \`\`\`" >> "${SUMMARY_FILE}"
echo "" >> "${SUMMARY_FILE}"
echo "3. **Fine-tune for Therapeutic Targets:**" >> "${SUMMARY_FILE}"
echo "   \`\`\`bash" >> "${SUMMARY_FILE}"
echo "   cd finetune_pinnacle" >> "${SUMMARY_FILE}"
echo "   python train.py --disease EFO_0000685 --embeddings_dir ../data/pinnacle_embeds/" >> "${SUMMARY_FILE}"
echo "   \`\`\`" >> "${SUMMARY_FILE}"

echo "✓ Summary report generated: ${SUMMARY_FILE}"

# Final status report
echo ""
echo "=================================================================="
echo "CD4-PINNACLE PREPROCESSING PIPELINE COMPLETED"
echo "=================================================================="
echo ""
echo "Pipeline completed at: $(date)"
echo "Total runtime: $SECONDS seconds"
echo ""
echo "📁 Key Output Files:"
echo "   - Gene Rankings: ${NETWORKS_DIR}/cd4_ranked_genes.csv"
echo "   - PPI Networks: ${NETWORKS_DIR}/cd4_celltype_ppi_pval1.0_genes4000.csv"
echo "   - Summary Report: ${SUMMARY_FILE}"
echo "   - Full Log: ${LOG_FILE}"
echo ""
echo "🚀 Ready for CD4-PINNACLE model training!"
echo ""
echo "Next command:"
echo "   cd pinnacle && bash run_pinnacle.sh"
echo ""
echo "=================================================================="